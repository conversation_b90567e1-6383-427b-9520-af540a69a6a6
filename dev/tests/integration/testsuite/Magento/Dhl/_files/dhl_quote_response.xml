<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<res:DCTResponse xmlns:res="http://www.dhl.com"
                 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                 xsi:schemaLocation="http://www.dhl.com DCT-Response_global-2.0.xsd">
    <GetQuoteResponse>
        <Response>
            <ServiceHeader>
                <MessageTime>2014-01-09T12:13:29.498+00:00</MessageTime>
                <SiteID>EvgeniyUSA</SiteID>
            </ServiceHeader>
        </Response>
        <BkgDetails>
            <QtdShp>
                <OriginServiceArea>
                    <FacilityCode>NUQ</FacilityCode>
                    <ServiceAreaCode>NUQ</ServiceAreaCode>
                </OriginServiceArea>
                <DestinationServiceArea>
                    <FacilityCode>BER</FacilityCode>
                    <ServiceAreaCode>BER</ServiceAreaCode>
                </DestinationServiceArea>
                <GlobalProductCode>E</GlobalProductCode>
                <LocalProductCode>E</LocalProductCode>
                <ProductShortName>EXPRESS 9:00</ProductShortName>
                <LocalProductName>EXPRESS 9:00 NONDOC</LocalProductName>
                <NetworkTypeCode>TD</NetworkTypeCode>
                <POfferedCustAgreement>N</POfferedCustAgreement>
                <TransInd>Y</TransInd>
                <PickupDate>2014-01-09</PickupDate>
                <PickupCutoffTime>PT16H15M</PickupCutoffTime>
                <BookingTime>PT15H15M</BookingTime>
                <CurrencyCode>USD</CurrencyCode>
                <ExchangeRate>1.000000</ExchangeRate>
                <WeightCharge>42.060</WeightCharge>
                <WeightChargeTax>0.000</WeightChargeTax>
                <TotalTransitDays>2</TotalTransitDays>
                <PickupPostalLocAddDays>0</PickupPostalLocAddDays>
                <DeliveryPostalLocAddDays>0</DeliveryPostalLocAddDays>
                <DeliveryDate>
                    <DlvyDateTime>2014-01-13 11:59:00</DlvyDateTime>
                    <DeliveryDateTimeOffset>+00:00</DeliveryDateTimeOffset>
                </DeliveryDate>
                <DeliveryTime>PT9H</DeliveryTime>
                <DimensionalWeight>2.205</DimensionalWeight>
                <WeightUnit>LB</WeightUnit>
                <PickupDayOfWeekNum>4</PickupDayOfWeekNum>
                <DestinationDayOfWeekNum>1</DestinationDayOfWeekNum>
                <QtdShpExChrg>
                    <SpecialServiceType>FF</SpecialServiceType>
                    <LocalServiceType>FF</LocalServiceType>
                    <GlobalServiceName>FUEL SURCHARGE</GlobalServiceName>
                    <LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName>
                    <ChargeCodeType>SCH</ChargeCodeType>
                    <CurrencyCode>USD</CurrencyCode>
                    <ChargeValue>3.790</ChargeValue>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>3.790</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>3.790</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>PULCL</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>3.790</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>BASEC</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                </QtdShpExChrg>
                <PricingDate>2014-01-09</PricingDate>
                <ShippingCharge>45.850</ShippingCharge>
                <TotalTaxAmount>0.000</TotalTaxAmount>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode>
                    <WeightCharge>42.060</WeightCharge>
                    <TotalAmount>45.850</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>PULCL</CurrencyRoleTypeCode>
                    <WeightCharge>42.060</WeightCharge>
                    <TotalAmount>45.850</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>BASEC</CurrencyRoleTypeCode>
                    <WeightCharge>42.060</WeightCharge>
                    <TotalAmount>45.850</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <PickupWindowEarliestTime>09:00:00</PickupWindowEarliestTime>
                <PickupWindowLatestTime>17:00:00</PickupWindowLatestTime>
                <BookingCutoffOffset>PT1H</BookingCutoffOffset>
            </QtdShp>
            <QtdShp>
                <OriginServiceArea>
                    <FacilityCode>NUQ</FacilityCode>
                    <ServiceAreaCode>NUQ</ServiceAreaCode>
                </OriginServiceArea>
                <DestinationServiceArea>
                    <FacilityCode>BER</FacilityCode>
                    <ServiceAreaCode>BER</ServiceAreaCode>
                </DestinationServiceArea>
                <GlobalProductCode>Q</GlobalProductCode>
                <LocalProductCode>Q</LocalProductCode>
                <ProductShortName>MEDICAL EXPRESS</ProductShortName>
                <LocalProductName>MEDICAL EXPRESS</LocalProductName>
                <NetworkTypeCode>TD</NetworkTypeCode>
                <POfferedCustAgreement>Y</POfferedCustAgreement>
                <TransInd>N</TransInd>
                <PickupDate>2014-01-09</PickupDate>
                <PickupCutoffTime>PT16H15M</PickupCutoffTime>
                <BookingTime>PT15H15M</BookingTime>
                <CurrencyCode>USD</CurrencyCode>
                <ExchangeRate>1.000000</ExchangeRate>
                <WeightCharge>32.350</WeightCharge>
                <WeightChargeTax>0.000</WeightChargeTax>
                <TotalTransitDays>2</TotalTransitDays>
                <PickupPostalLocAddDays>0</PickupPostalLocAddDays>
                <DeliveryPostalLocAddDays>0</DeliveryPostalLocAddDays>
                <DeliveryDate>
                    <DlvyDateTime>2014-01-13 11:59:00</DlvyDateTime>
                    <DeliveryDateTimeOffset>+00:00</DeliveryDateTimeOffset>
                </DeliveryDate>
                <DeliveryTime>PT9H</DeliveryTime>
                <DimensionalWeight>2.205</DimensionalWeight>
                <WeightUnit>LB</WeightUnit>
                <PickupDayOfWeekNum>4</PickupDayOfWeekNum>
                <DestinationDayOfWeekNum>1</DestinationDayOfWeekNum>
                <QtdShpExChrg>
                    <SpecialServiceType>FF</SpecialServiceType>
                    <LocalServiceType>FF</LocalServiceType>
                    <GlobalServiceName>FUEL SURCHARGE</GlobalServiceName>
                    <LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName>
                    <ChargeCodeType>SCH</ChargeCodeType>
                    <CurrencyCode>USD</CurrencyCode>
                    <ChargeValue>2.910</ChargeValue>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>2.910</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>2.910</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>PULCL</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>2.910</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>BASEC</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                </QtdShpExChrg>
                <PricingDate>2014-01-09</PricingDate>
                <ShippingCharge>35.260</ShippingCharge>
                <TotalTaxAmount>0.000</TotalTaxAmount>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode>
                    <WeightCharge>32.350</WeightCharge>
                    <TotalAmount>35.260</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>PULCL</CurrencyRoleTypeCode>
                    <WeightCharge>32.350</WeightCharge>
                    <TotalAmount>35.260</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>BASEC</CurrencyRoleTypeCode>
                    <WeightCharge>32.350</WeightCharge>
                    <TotalAmount>35.260</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <PickupWindowEarliestTime>09:00:00</PickupWindowEarliestTime>
                <PickupWindowLatestTime>17:00:00</PickupWindowLatestTime>
                <BookingCutoffOffset>PT1H</BookingCutoffOffset>
            </QtdShp>
            <QtdShp>
                <OriginServiceArea>
                    <FacilityCode>NUQ</FacilityCode>
                    <ServiceAreaCode>NUQ</ServiceAreaCode>
                </OriginServiceArea>
                <DestinationServiceArea>
                    <FacilityCode>BER</FacilityCode>
                    <ServiceAreaCode>BER</ServiceAreaCode>
                </DestinationServiceArea>
                <GlobalProductCode>Y</GlobalProductCode>
                <LocalProductCode>Y</LocalProductCode>
                <ProductShortName>EXPRESS 12:00</ProductShortName>
                <LocalProductName>EXPRESS 12:00 NONDOC</LocalProductName>
                <NetworkTypeCode>TD</NetworkTypeCode>
                <POfferedCustAgreement>N</POfferedCustAgreement>
                <TransInd>Y</TransInd>
                <PickupDate>2014-01-09</PickupDate>
                <PickupCutoffTime>PT16H15M</PickupCutoffTime>
                <BookingTime>PT15H15M</BookingTime>
                <CurrencyCode>USD</CurrencyCode>
                <ExchangeRate>1.000000</ExchangeRate>
                <WeightCharge>34.290</WeightCharge>
                <WeightChargeTax>0.000</WeightChargeTax>
                <TotalTransitDays>2</TotalTransitDays>
                <PickupPostalLocAddDays>0</PickupPostalLocAddDays>
                <DeliveryPostalLocAddDays>0</DeliveryPostalLocAddDays>
                <DeliveryDate>
                    <DlvyDateTime>2014-01-13 11:59:00</DlvyDateTime>
                    <DeliveryDateTimeOffset>+00:00</DeliveryDateTimeOffset>
                </DeliveryDate>
                <DeliveryTime>PT12H</DeliveryTime>
                <DimensionalWeight>2.205</DimensionalWeight>
                <WeightUnit>LB</WeightUnit>
                <PickupDayOfWeekNum>4</PickupDayOfWeekNum>
                <DestinationDayOfWeekNum>1</DestinationDayOfWeekNum>
                <QtdShpExChrg>
                    <SpecialServiceType>FF</SpecialServiceType>
                    <LocalServiceType>FF</LocalServiceType>
                    <GlobalServiceName>FUEL SURCHARGE</GlobalServiceName>
                    <LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName>
                    <ChargeCodeType>SCH</ChargeCodeType>
                    <CurrencyCode>USD</CurrencyCode>
                    <ChargeValue>3.090</ChargeValue>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>3.090</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>3.090</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>PULCL</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>3.090</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>BASEC</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                </QtdShpExChrg>
                <PricingDate>2014-01-09</PricingDate>
                <ShippingCharge>37.380</ShippingCharge>
                <TotalTaxAmount>0.000</TotalTaxAmount>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode>
                    <WeightCharge>34.290</WeightCharge>
                    <TotalAmount>37.380</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>PULCL</CurrencyRoleTypeCode>
                    <WeightCharge>34.290</WeightCharge>
                    <TotalAmount>37.380</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>BASEC</CurrencyRoleTypeCode>
                    <WeightCharge>34.290</WeightCharge>
                    <TotalAmount>37.380</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <PickupWindowEarliestTime>09:00:00</PickupWindowEarliestTime>
                <PickupWindowLatestTime>17:00:00</PickupWindowLatestTime>
                <BookingCutoffOffset>PT1H</BookingCutoffOffset>
            </QtdShp>
            <QtdShp>
                <OriginServiceArea>
                    <FacilityCode>NUQ</FacilityCode>
                    <ServiceAreaCode>NUQ</ServiceAreaCode>
                </OriginServiceArea>
                <DestinationServiceArea>
                    <FacilityCode>BER</FacilityCode>
                    <ServiceAreaCode>BER</ServiceAreaCode>
                </DestinationServiceArea>
                <GlobalProductCode>3</GlobalProductCode>
                <LocalProductCode>3</LocalProductCode>
                <ProductShortName>B2C</ProductShortName>
                <LocalProductName>EXPRESS WORLDWIDE (B2C)</LocalProductName>
                <NetworkTypeCode>TD</NetworkTypeCode>
                <POfferedCustAgreement>Y</POfferedCustAgreement>
                <TransInd>N</TransInd>
                <PickupDate>2014-01-09</PickupDate>
                <PickupCutoffTime>PT16H15M</PickupCutoffTime>
                <BookingTime>PT15H15M</BookingTime>
                <ExchangeRate>1.000000</ExchangeRate>
                <WeightCharge>0</WeightCharge>
                <WeightChargeTax>0.000</WeightChargeTax>
                <TotalTransitDays>2</TotalTransitDays>
                <PickupPostalLocAddDays>0</PickupPostalLocAddDays>
                <DeliveryPostalLocAddDays>0</DeliveryPostalLocAddDays>
                <DeliveryDate>
                    <DlvyDateTime>2014-01-13 11:59:00</DlvyDateTime>
                    <DeliveryDateTimeOffset>+00:00</DeliveryDateTimeOffset>
                </DeliveryDate>
                <DeliveryTime>PT23H59M</DeliveryTime>
                <DimensionalWeight>2.205</DimensionalWeight>
                <WeightUnit>LB</WeightUnit>
                <PickupDayOfWeekNum>4</PickupDayOfWeekNum>
                <DestinationDayOfWeekNum>1</DestinationDayOfWeekNum>
                <PricingDate>2014-01-09</PricingDate>
                <ShippingCharge>0.000</ShippingCharge>
                <TotalTaxAmount>0.000</TotalTaxAmount>
                <QtdSInAdCur>
                    <CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode>
                    <WeightCharge>0</WeightCharge>
                    <TotalAmount>0.000</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>PULCL</CurrencyRoleTypeCode>
                    <WeightCharge>0</WeightCharge>
                    <TotalAmount>0.000</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>BASEC</CurrencyRoleTypeCode>
                    <WeightCharge>0</WeightCharge>
                    <TotalAmount>0.000</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <PickupWindowEarliestTime>09:00:00</PickupWindowEarliestTime>
                <PickupWindowLatestTime>17:00:00</PickupWindowLatestTime>
                <BookingCutoffOffset>PT1H</BookingCutoffOffset>
            </QtdShp>
            <QtdShp>
                <OriginServiceArea>
                    <FacilityCode>NUQ</FacilityCode>
                    <ServiceAreaCode>NUQ</ServiceAreaCode>
                </OriginServiceArea>
                <DestinationServiceArea>
                    <FacilityCode>BER</FacilityCode>
                    <ServiceAreaCode>BER</ServiceAreaCode>
                </DestinationServiceArea>
                <GlobalProductCode>P</GlobalProductCode>
                <LocalProductCode>P</LocalProductCode>
                <ProductShortName>EXPRESS WORLDWIDE</ProductShortName>
                <LocalProductName>EXPRESS WORLDWIDE NONDOC</LocalProductName>
                <NetworkTypeCode>TD</NetworkTypeCode>
                <POfferedCustAgreement>N</POfferedCustAgreement>
                <TransInd>Y</TransInd>
                <PickupDate>2014-01-09</PickupDate>
                <PickupCutoffTime>PT16H15M</PickupCutoffTime>
                <BookingTime>PT15H15M</BookingTime>
                <CurrencyCode>USD</CurrencyCode>
                <ExchangeRate>1.000000</ExchangeRate>
                <WeightCharge>32.350</WeightCharge>
                <WeightChargeTax>0.000</WeightChargeTax>
                <TotalTransitDays>2</TotalTransitDays>
                <PickupPostalLocAddDays>0</PickupPostalLocAddDays>
                <DeliveryPostalLocAddDays>0</DeliveryPostalLocAddDays>
                <DeliveryDate>
                    <DlvyDateTime>2014-01-13 11:59:00</DlvyDateTime>
                    <DeliveryDateTimeOffset>+00:00</DeliveryDateTimeOffset>
                </DeliveryDate>
                <DeliveryTime>PT23H59M</DeliveryTime>
                <DimensionalWeight>2.205</DimensionalWeight>
                <WeightUnit>LB</WeightUnit>
                <PickupDayOfWeekNum>4</PickupDayOfWeekNum>
                <DestinationDayOfWeekNum>1</DestinationDayOfWeekNum>
                <QtdShpExChrg>
                    <SpecialServiceType>FF</SpecialServiceType>
                    <LocalServiceType>FF</LocalServiceType>
                    <GlobalServiceName>FUEL SURCHARGE</GlobalServiceName>
                    <LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName>
                    <ChargeCodeType>SCH</ChargeCodeType>
                    <CurrencyCode>USD</CurrencyCode>
                    <ChargeValue>2.910</ChargeValue>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>2.910</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>2.910</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>PULCL</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                    <QtdSExtrChrgInAdCur>
                        <ChargeValue>2.910</ChargeValue>
                        <CurrencyCode>USD</CurrencyCode>
                        <CurrencyRoleTypeCode>BASEC</CurrencyRoleTypeCode>
                    </QtdSExtrChrgInAdCur>
                </QtdShpExChrg>
                <PricingDate>2014-01-09</PricingDate>
                <ShippingCharge>35.260</ShippingCharge>
                <TotalTaxAmount>0.000</TotalTaxAmount>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>BILLC</CurrencyRoleTypeCode>
                    <WeightCharge>32.350</WeightCharge>
                    <TotalAmount>35.260</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>PULCL</CurrencyRoleTypeCode>
                    <WeightCharge>32.350</WeightCharge>
                    <TotalAmount>35.260</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <QtdSInAdCur>
                    <CurrencyCode>USD</CurrencyCode>
                    <CurrencyRoleTypeCode>BASEC</CurrencyRoleTypeCode>
                    <WeightCharge>32.350</WeightCharge>
                    <TotalAmount>35.260</TotalAmount>
                    <TotalTaxAmount>0.000</TotalTaxAmount>
                    <WeightChargeTax>0.000</WeightChargeTax>
                </QtdSInAdCur>
                <PickupWindowEarliestTime>09:00:00</PickupWindowEarliestTime>
                <PickupWindowLatestTime>17:00:00</PickupWindowLatestTime>
                <BookingCutoffOffset>PT1H</BookingCutoffOffset>
            </QtdShp>
        </BkgDetails>
        <Srvs>
            <Srv>
                <GlobalProductCode>E</GlobalProductCode>
                <MrkSrv>
                    <LocalProductCode>E</LocalProductCode>
                    <ProductShortName>EXPRESS 9:00</ProductShortName>
                    <LocalProductName>EXPRESS 9:00 NONDOC</LocalProductName>
                    <NetworkTypeCode>TD</NetworkTypeCode>
                    <POfferedCustAgreement>N</POfferedCustAgreement>
                    <TransInd>Y</TransInd>
                </MrkSrv>
                <MrkSrv>
                    <LocalServiceType>FF</LocalServiceType>
                    <GlobalServiceName>FUEL SURCHARGE</GlobalServiceName>
                    <LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName>
                    <ChargeCodeType>SCH</ChargeCodeType>
                    <MrkSrvInd>N</MrkSrvInd>
                </MrkSrv>
            </Srv>
            <Srv>
                <GlobalProductCode>Q</GlobalProductCode>
                <MrkSrv>
                    <LocalProductCode>Q</LocalProductCode>
                    <ProductShortName>MEDICAL EXPRESS</ProductShortName>
                    <LocalProductName>MEDICAL EXPRESS</LocalProductName>
                    <NetworkTypeCode>TD</NetworkTypeCode>
                    <POfferedCustAgreement>Y</POfferedCustAgreement>
                    <TransInd>N</TransInd>
                </MrkSrv>
                <MrkSrv>
                    <LocalServiceType>FF</LocalServiceType>
                    <GlobalServiceName>FUEL SURCHARGE</GlobalServiceName>
                    <LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName>
                    <ChargeCodeType>SCH</ChargeCodeType>
                    <MrkSrvInd>N</MrkSrvInd>
                </MrkSrv>
            </Srv>
            <Srv>
                <GlobalProductCode>Y</GlobalProductCode>
                <MrkSrv>
                    <LocalProductCode>Y</LocalProductCode>
                    <ProductShortName>EXPRESS 12:00</ProductShortName>
                    <LocalProductName>EXPRESS 12:00 NONDOC</LocalProductName>
                    <NetworkTypeCode>TD</NetworkTypeCode>
                    <POfferedCustAgreement>N</POfferedCustAgreement>
                    <TransInd>Y</TransInd>
                </MrkSrv>
                <MrkSrv>
                    <LocalServiceType>FF</LocalServiceType>
                    <GlobalServiceName>FUEL SURCHARGE</GlobalServiceName>
                    <LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName>
                    <ChargeCodeType>SCH</ChargeCodeType>
                    <MrkSrvInd>N</MrkSrvInd>
                </MrkSrv>
            </Srv>
            <Srv>
                <GlobalProductCode>3</GlobalProductCode>
                <MrkSrv>
                    <LocalProductCode>3</LocalProductCode>
                    <ProductShortName>B2C</ProductShortName>
                    <LocalProductName>EXPRESS WORLDWIDE (B2C)</LocalProductName>
                    <NetworkTypeCode>TD</NetworkTypeCode>
                    <POfferedCustAgreement>Y</POfferedCustAgreement>
                    <TransInd>N</TransInd>
                </MrkSrv>
            </Srv>
            <Srv>
                <GlobalProductCode>P</GlobalProductCode>
                <MrkSrv>
                    <LocalProductCode>P</LocalProductCode>
                    <ProductShortName>EXPRESS WORLDWIDE</ProductShortName>
                    <LocalProductName>EXPRESS WORLDWIDE NONDOC</LocalProductName>
                    <NetworkTypeCode>TD</NetworkTypeCode>
                    <POfferedCustAgreement>N</POfferedCustAgreement>
                    <TransInd>Y</TransInd>
                </MrkSrv>
                <MrkSrv>
                    <LocalServiceType>FF</LocalServiceType>
                    <GlobalServiceName>FUEL SURCHARGE</GlobalServiceName>
                    <LocalServiceTypeName>FUEL SURCHARGE</LocalServiceTypeName>
                    <ChargeCodeType>SCH</ChargeCodeType>
                    <MrkSrvInd>N</MrkSrvInd>
                </MrkSrv>
            </Srv>
        </Srvs>
    </GetQuoteResponse>
</res:DCTResponse>
