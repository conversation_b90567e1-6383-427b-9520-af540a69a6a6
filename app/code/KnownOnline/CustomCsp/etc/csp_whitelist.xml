<?xml version="1.0" encoding="UTF-8"?>
<csp_whitelist xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
               xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Csp:etc/csp_whitelist.xsd">
    <policies>
        <policy id="script-src">
            <values>
                <value id="api-smooch" type="host">https://api.smooch.io</value>
                <value id="api-smooch-faye" type="host">https://api.smooch.io/faye</value>
                <value id="zendesk-coliseum" type="host">https://coliseumstorehelp.zendesk.com</value>
                <value id="wcx-api-script" type="host">https://api.wcx.cloud</value>
                <value id="wcentrix-script" type="host">https://f.wcentrix.com</value>
            </values>
        </policy>
        <policy id="style-src">
            <values>
                <value id="wcentrix-style" type="host">https://f.wcentrix.com</value>
            </values>
        </policy>
        <policy id="img-src">
            <values>
                <value id="google" type="host">*.google.com</value>
                <value id="google-com-ar" type="host">https://www.google.com.ar</value>
                <value id="google-cl" type="host">https://www.google.cl</value>
                <value id="google-com-pe" type="host">https://www.google.com.pe</value>
                <value id="google-co-ve" type="host">https://www.google.co.ve</value>
                <value id="zendesk-coliseum" type="host">https://coliseumstorehelp.zendesk.com</value>
                <value id="wcx-img" type="host">https://api.wcx.cloud</value>
                <value id="wcentrix-img" type="host">https://f.wcentrix.com</value>
            </values>
        </policy>
        <policy id="connect-src">
            <values>
                <value id="api-smooch-faye" type="host">wss://api.smooch.io</value>
                <value id="zendesk-coliseum" type="host">https://coliseumstorehelp.zendesk.com</value>
                <value id="wcx-api-connect" type="host">https://api.wcx.cloud</value>
                <value id="wcentrix-connect" type="host">https://f.wcentrix.com</value>
            </values>
        </policy>
        <policy id="frame-src">
            <values>
                <value id="wcx-frame" type="host">https://api.wcx.cloud</value>
                <value id="wcentrix-frame" type="host">https://f.wcentrix.com</value>
            </values>
        </policy>
        <policy id="worker-src">
            <values>
                <value id="wcx-worker" type="host">https://api.wcx.cloud</value>
                <value id="wcentrix-worker" type="host">https://f.wcentrix.com</value>
            </values>
        </policy>
        <policy id="font-src">
            <values>
                <value id="wcentrix-font" type="host">https://f.wcentrix.com</value>
            </values>
        </policy>
    </policies>
</csp_whitelist>
