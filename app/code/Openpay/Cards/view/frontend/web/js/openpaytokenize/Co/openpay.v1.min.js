(function(){var u,a={}.hasOwnProperty,n=function(e,t){function n(){this.constructor=t}for(var r in e)a.call(e,r)&&(t[r]=e[r]);return n.prototype=e.prototype,t.prototype=new n,t.__super__=e.prototype,t},e=this;this.OpenPay=function(){function m(){}function g(e,t,n,r,a){clearTimeout(t);var o=null;n=n||"Unknown error",r=r||0,a=a||"{}";try{o=JSON.parse(a)}catch(i){n="Response error"}e({message:n,status:r,data:o,toString:function(){return this.message+" [status "+this.status+"]"}})}function i(e,t,n){return void 0!==t&&/^[a-z0-9]+$/i.test(t)?!(void 0===n||!/^pk_[a-z0-9]+$/i.test(n))||(g(e,null,"Empty or invalid Openpay API Key"),!1):(g(e,null,"Empty or invalid Openpay ID"),!1)}function u(e,t,n,r,a,o){var i=null,u=null,s="",c={};if("function"!=typeof r&&(r=m.log),"function"!=typeof a&&(a=m.log),"undefined"!=typeof JSON){4e4;var d=XMLHttpRequest&&"withCredentials"in new XMLHttpRequest;if(d){function p(){if("undefined"!=typeof i.readyState&&4==i.readyState||!d)if(clearTimeout(u),i.status<200||300<=i.status)g(a,u,"Request error",i.status,i.responseText);else{var e;try{e=JSON.parse(i.responseText)}catch(t){g(a,u,"Response error (JSON parse failed)",i.status,"{}")}r({data:e,status:200})}}if(!(i=function h(){if(function r(e,t){var n=typeof e[t];return"function"==n||!("object"!=n||!e[t])||"unknown"==n}(window,"XMLHttpRequest"))return new XMLHttpRequest}()))return void g(a,u,"Browser error (CORS not supported)");for(var l in s=JSON.stringify(n),c={Accept:"application/json","Content-Type":"application/json",Authorization:"Basic "+t},null==o&&(o="POST"),i.open(o,e,!0),"withCredentials"in i&&(i.withCredentials=!0),c)c.hasOwnProperty(l)&&c[l]&&"setRequestHeader"in i&&i.setRequestHeader(l,c[l]);"onreadystatechange"in i?i.onreadystatechange=p:"onload"in i&&"onerror"in i&&(i.onload=p,i.onerror=g),u=setTimeout(function(){"onload"in i?i.onload=Function.prototype:i.onreadystatechange=Function.prototype,i.abort(),i=null,g(a,u,"Timeout after 40000 milliseconds")},4e4),i.send(s)}else{if("undefined"==typeof XDomainRequest)return void g(a,u,"Browser error (CORS not supported)");n.apiKey=t;var p,f={callbackName:"getResultData",onSuccess:p=function(e){e.error?g(a,u,"Request error",e.httpStatus,JSON.stringify(e)):r({data:e.data,status:200})},onError:g,timeout:4e4,url:e+"/jsonp",data:n};$jsonp.send(f)}}else g(a,u,"Browser error (JSON library not found)")}function s(e){return btoa(e+":")}function c(){return m.sandboxMode?m.sandboxHostname:m.developMode?m.developHostname:m.hostname}return m.version=1,m.sandboxMode=!1,m.developMode=!1,m.hostname="https://api.openpay.co/v1/",m.sandboxHostname="https://sandbox-api.openpay.co/v1/",m.developHostname="https://dev-api.openpay.co/v1/",m.Group={},m.Update={},m.setSandboxMode=function(e){m.sandboxMode=!!e,e&&(m.developMode=!1)},m.getSandboxMode=function(){return m.sandboxMode},m.setDevelopMode=function(e){m.developMode=!!e,e&&(m.sandboxMode=!1)},m.getDevelopMode=function(){return m.developMode},m.setId=function(e){m.id=e},m.getId=function(){return m.id},m.setApiKey=function(e){m.key=e},m.getApiKey=function(){return m.key},m.Group.setId=function(e){m.Group.id=e},m.Group.getId=function(){return m.Group.id},m.Group.setApiKey=function(e){m.Group.key=e},m.Group.getApiKey=function(){return m.Group.key},m.log=function(e){"object"==typeof e&&"toString"in e&&(e=e.toString()),"undefined"!=typeof console&&"log"in console&&console.log(e)},m.validate=function(e,t){if(!e)throw t+" required";if("object"!=typeof e)throw t+" invalid"},m.formatData=function(e,t){return e},m.extractFormInfo=function(e){var t,n,r,a,i=function(e,t){var n,r,a=[],o=e.children;for(r=0;r<o.length;r++)1===(n=o[r]).nodeType&&n.attributes[t]?a.push(n):0<n.children.length&&(a=a.concat(i(n,t)));return a},o=function(e,t){for(var n={},r=0;r<e.length;r++)fieldName=e[r].attributes[t].value,inputValue=e[r].value,n[fieldName]!==undefined?(n[fieldName].push||(n[this.name]=[n[this.name]]),n[fieldName].push(inputValue||"")):n[fieldName]=inputValue||"";return n};return a=e,e=window.jQuery&&a instanceof jQuery?a[0]:a.nodeType&&1===a.nodeType?a:document.getElementById(a),t=o(i(e,"data-openpay-card"),"data-openpay-card"),(r=i(e,"data-openpay-card-address"))!==undefined&&r.length&&0<r.length&&(n=o(r,"data-openpay-card-address"))!==undefined&&(t.address=n),t},m.send=function(e,t,n,r){if(i(r,m.id,m.key)){var a=s(m.key),o=c();return u(o=o+m.id+"/"+e,a,t,n,r)}},m.Group.send=function(e,t,n,r){if(i(r,m.Group.id,m.Group.key)){var a=s(m.Group.key),o=c();return u(o=o+"groups/"+m.Group.id+"/"+e,a,t,n,r)}},m.Update.send=function(e,t,n,r,a){var o="";if(e){if(!i(r,m.Group.id,m.Group.key))return;o=s(m.Group.key)}else{if(!i(r,m.id,m.key))return;o=s(m.key)}return u(c()+a,o,t,n,r,"PUT")},m}.call(this),u=this.OpenPay,this.OpenPay.card=function(e){function i(){return i.__super__.constructor.apply(this,arguments)}return n(e,i),i.validateCardNumber=function(e){return e=(e+"").replace(/\s+|-/g,""),/^\d+$/.test(e)&&10<=e.length&&e.length<=19&&i.luhnCheck(e)&&i.validateCardNumberLength(e)&&i.validateAcceptCardNumber(e)},i.validateCVC=function(e,t){switch(arguments.length){case 1:return e=u.utils.trim(e),/^\d+$/.test(e)&&3<=e.length&&e.length<=4;case 2:return"American Express"==i.cardType(t)?(e=u.utils.trim(e),/^\d+$/.test(e)&&4==e.length):(e=u.utils.trim(e),/^\d+$/.test(e)&&3==e.length);default:return!1}},i.validateExpiry=function(e,t){var n,r;return 2===(t=u.utils.trim(t)).length&&(t="20"+t),e=u.utils.trim(e),!!/^\d+$/.test(e)&&(!!/^\d+$/.test(t)&&(parseInt(e,10)<=12&&1<=parseInt(e,10)&&(r=new Date(t,e),n=new Date,r.setMonth(r.getMonth()-1),r.setMonth(r.getMonth()+1,1),n<r)))},i.validateCardNumberLength=function(e){var t;if(t=i.cardAbstract(e)){for(var n=t.length.length;n--;)if(t.length[n]==e.length)return!0;return!1}return 10<=e.length&&e.length<=19},i.validateAcceptCardNumber=function(e){return!0},i.luhnCheck=function(e){for(var t=(e+"").split(""),n=0,r=parseInt(t[e.length-1]),a=t.length-2,o=1;0<=a;a--,o++)n=parseInt(t[a]),o%2!=0&&9<(n*=2)&&(n-=9),r+=n;return r%10==0},i.cardType=function(e){var t;return(t=i.cardAbstract(e))?t.name:""},i.cardTypes=function(){return{visa_electron:{name:"Visa Electron",regx:/^(4026|417500|4508|4844|491(3|7))/,length:[16],accept:!0},visa:{name:"Visa",regx:/^4/,length:[16],accept:!0},mastercard:{name:"Mastercard",regx:/^5[1-5]/,length:[16],accept:!0},amex:{name:"American Express",regx:/^3[47]/,length:[15],accept:!0},diners_cb:{name:"Diners Club Carte Blanche",regx:/^30[0-5]/,length:[14],accept:!0},diners_int:{name:"Diners Club International",regx:/^36/,length:[14],accept:!0},jcb:{name:"JCB",regx:/^35(2[89]|[3-8][0-9])/,length:[16],accept:!0},laser:{name:"Laser",regx:/^(6304|670[69]|6771)/,length:[16,17,18,19],accept:!0},maestro:{name:"Maestro",regx:/^(5018|5020|5038|6304|6759|676[1-3])/,length:[12,13,14,15,16,17,18,19],accept:!0},discover:{name:"Discover",regx:/^(6011|622(12[6-9]|1[3-9][0-9]|[2-8][0-9]{2}|9[0-1][0-9]|92[0-5]|64[4-9])|65)/,length:[16],accept:!0}}},i.cardAbstract=function(e){var t;for(var n in t=i.cardTypes())if(_cardObj=t[n],e.match(_cardObj.regx))return _cardObj;return!1},i.whitelistedAttrs=["holder_name","cvv2","expiration_month","expiration_year"],i.extractFormAndUpdateCard=function(e,t,n,r,a){var o=u.extractFormInfo(e);return i.update(o,t,n,r,a)},i.update=function(e,t,n,r,a){var o="/"+this.getId();return null!=r&&(o=o+"/customers/"+r),null!=a&&(o=o+"/cards/"+a),u.formatData(e,i.whitelistedAttrs),u.Update.send(!1,e,t,n,o)},i}.call(this,this.OpenPay),this.OpenPay.token=function(e){function o(){return o.__super__.constructor.apply(this,arguments)}return n(e,o),o.whitelistedAttrs=["card_number","holder_name","cvv2","expiration_month","expiration_year","address"],o.extractFormAndCreate=function(e,t,n,r){var a=u.extractFormInfo(e);return o.create(a,t,n)},o.create=function(e,t,n){return u.validate(e,"tarjeta"),u.formatData(e,o.whitelistedAttrs),u.send("tokens",e,t,n)},o}.call(this,this.OpenPay),this.OpenPay.Group.token=function(e){function o(){return o.__super__.constructor.apply(this,arguments)}return n(e,o),o.whitelistedAttrs=["card_number","holder_name","cvv2","expiration_month","expiration_year","address"],o.extractFormAndCreate=function(e,t,n,r){var a=u.extractFormInfo(e);return o.create(a,t,n)},o.create=function(e,t,n){return u.validate(e,"tarjeta"),u.formatData(e,o.whitelistedAttrs),u.Group.send("tokens",e,t,n)},o}.call(this,this.OpenPay),this.OpenPay.Group.card=function(e){function i(){return i.__super__.constructor.apply(this,arguments)}return n(e,i),i.whitelistedAttrs=["holder_name","cvv2","expiration_month","expiration_year"],i.extractFormAndUpdateCard=function(e,t,n,r,a){var o=u.extractFormInfo(e);return i.update(o,t,n,r,a)},i.update=function(e,t,n,r,a){var o="groups/"+this.Group.id+"/customers/"+r+"/cards/"+a;return e.merchant_id||(e.merchant_id=this.getId()),u.formatData(e,i.whitelistedAttrs),u.Update.send(!0,e,t,n,o)},i}.call(this,this.OpenPay),this.OpenPay.utils=function(e){function t(){}return n(e,t),t.trim=function(e){return(e+"").replace(/^\s+|\s+$/g,"")},t.underscore=function(e){return(e+"").replace(/([A-Z])/g,function(e){return"_"+e.toLowerCase()}).replace(/-/g,"_")},t.underscoreKeys=function(e){var t,n;for(var r in n=[],e)t=e[r],delete e[r],n.push(e[this.underscore(r)]=t);return n},t.isElement=function(e){return"object"==typeof e&&("undefined"!=typeof jQuery&&null!==jQuery&&e instanceof jQuery||1===e.nodeType)},t}.call(this,this.OpenPay),"undefined"!=typeof module&&null!==module&&(module.exports=this.OpenPay),"function"==typeof define&&define("openpay",[],function(){return e.OpenPay})}).call(this);var base64={PADCHAR:"=",ALPHA:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",makeDOMException:function(){try{return new DOMException(DOMException.INVALID_CHARACTER_ERR)}catch(t){var e=new Error("DOM Exception 5");return e.code=e.number=5,e.name=e.description="INVALID_CHARACTER_ERR",e.toString=function(){return"Error: "+e.name+": "+e.message},e}},getbyte64:function(e,t){var n=base64.ALPHA.indexOf(e.charAt(t));if(-1===n)throw base64.makeDOMException();return n},decode:function(e){e=""+e;var t,n,r,a=base64.getbyte64,o=e.length;if(0===o)return e;if(o%4!=0)throw base64.makeDOMException();t=0,e.charAt(o-1)===base64.PADCHAR&&(t=1,e.charAt(o-2)===base64.PADCHAR&&(t=2),o-=4);var i=[];for(n=0;n<o;n+=4)r=a(e,n)<<18|a(e,n+1)<<12|a(e,n+2)<<6|a(e,n+3),i.push(String.fromCharCode(r>>16,r>>8&255,255&r));switch(t){case 1:r=a(e,n)<<18|a(e,n+1)<<12|a(e,n+2)<<6,i.push(String.fromCharCode(r>>16,r>>8&255));break;case 2:r=a(e,n)<<18|a(e,n+1)<<12,i.push(String.fromCharCode(r>>16))}return i.join("")},getbyte:function(e,t){var n=e.charCodeAt(t);if(255<n)throw base64.makeDOMException();return n},encode:function(e){if(1!==arguments.length)throw new SyntaxError("Not enough arguments");var t,n,r=base64.PADCHAR,a=base64.ALPHA,o=base64.getbyte,i=[],u=(e=""+e).length-e.length%3;if(0===e.length)return e;for(t=0;t<u;t+=3)n=o(e,t)<<16|o(e,t+1)<<8|o(e,t+2),i.push(a.charAt(n>>18)),i.push(a.charAt(n>>12&63)),i.push(a.charAt(n>>6&63)),i.push(a.charAt(63&n));switch(e.length-u){case 1:n=o(e,t)<<16,i.push(a.charAt(n>>18)+a.charAt(n>>12&63)+r+r);break;case 2:n=o(e,t)<<16|o(e,t+1)<<8,i.push(a.charAt(n>>18)+a.charAt(n>>12&63)+a.charAt(n>>6&63)+r)}return i.join("")}};window.btoa||(window.btoa=base64.encode),window.atob||(window.atob=base64.decode);var $jsonp=function(){var e={};return e.send=function(e){var t=(new Date).getTime(),n=e.callbackName||"callback",r=e.onSuccess||function(){},a=e.onError||function(){},o=e.timeout||4e3,i=e.url||"",u=e.data||{};dataIdName="idData";var s=encodeURIComponent;u.callback="var "+dataIdName+"="+ ++t+";"+n,window[dataIdName]=undefined;var c=function(e,t,n){var r,a;for(r in null==t&&(t=[]),e)a=e[r],n&&(r=n+"."+r),"object"==typeof a?c(a,t,r):t.push((""+r).replace(/[\-_\s]+(.)?/g,function(e,t){return t?t.toUpperCase():""}).replace(/^([A-Z])/,function(e,t){return t?t.toLowerCase():""})+"="+s(a));return t.join("&").replace(/%20/g,"+")};timeout_trigger=window.setTimeout(function(){window[n]=function(){},a("Timeout after "+o+" milliseconds")},o);var d=document.createElement("script");d.type="text/javascript",d.async=!0,d.src=i+"?"+c(u);var p=function(){var e;null!=(e=d.parentNode)&&e.removeChild(d)},l=function(){window.clearTimeout(timeout_trigger),p(),a("There was an error, please verify your authentication data or conection.")};"onreadystatechange"in d?d.onreadystatechange=function(){"loaded"==d.readyState&&"undefined"==typeof window[dataIdName]&&l()}:"onerror"in d&&(d.onerror=function(){l()}),window[n]=function(e){window.clearTimeout(timeout_trigger),p(),r(e)},document.getElementsByTagName("head")[0].appendChild(d)},e}();"undefined"!=typeof jQuery&&function(c){void 0!==c&&(c.fn.cardNumberInput=function(){return c(this).restrictedInput("card")},c.fn.numericInput=function(){return c(this).restrictedInput("numeric")},c.fn.restrictedInput=function(e){var t=c(this),r=[{type:"amex",regex:/^3[47]/,format:/(\d{1,4})(\d{1,6})?(\d{1,5})?/,length:15},{type:"other",format:/(\d{1,4})/g,length:16}],s=function(e){var t,n;for(e=(e+"").replace(/\D/g,""),n=0;n<r.length;n++)if((t=r[n]).regex&&t.regex.test(e))return t},a=function(e){var t;return!(!e.metaKey&&!e.ctrlKey)||(0===e.which||32!==e.which&&(e.which<33||(t=String.fromCharCode(e.which),!!/[\d\s]/.test(t))))},n=function(e){var t,n,r=(c(e.currentTarget).val()+String.fromCharCode(e.which)).replace(/\D/g,"");return n=a(e),(t=s(r))?n&&r.length<=t.length:n&&r.length<=16},o=function(e){var t,n=c(e.currentTarget),r=n.val(),a=String.fromCharCode(e.which),o=(r.replace(/\D/g,"")+a).length,i=16,u=s(r+a);if(/^\d+$/.test(a)&&(u&&(i=u.length),!(i<=o)))return(t=u&&"amex"===u.type?/^(\d{4}|\d{4}\s\d{6})$/:/(?:^|\s)(\d{4})$/).test(r)?(e.preventDefault(),setTimeout(function(){return n.val(r+" "+a)})):t.test(r+a)?(e.preventDefault(),setTimeout(function(){return n.val(r+a+" ")})):void 0};return"card"===e?t.on("keypress",n).on("keypress",o).on("keydown",function(e){var t=c(e.currentTarget),n=t.val();if(8===e.which)return/\d\s$/.test(n)?(e.preventDefault(),setTimeout(function(){return t.val(n.replace(/\d\s$/,""))})):/\s\d?$/.test(n)?(e.preventDefault(),setTimeout(function(){return t.val(n.replace(/\s\d?$/,""))})):void 0}).on("paste",n).on("paste",o).on("change",o).on("input",o):"numeric"===e&&t.on("keypress",a).on("paste",a).on("change",a).on("input",a),this})}(jQuery);
