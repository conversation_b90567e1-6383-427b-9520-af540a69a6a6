(function(){var l=function(){},t=function(){},e=function(){};function d(){return l._deviceDataId===undefined&&(l._deviceDataId=b.codec.base64.fromBits(b.random.randomWords(6,0)).replace(/[\+\/]/g,"0")),l._deviceDataId}function u(){var t=d();return function r(t,e,n){var o=t+"antifraud/"+e+"/components?s="+n;if(window.XDomainRequest){var i=new XDomainRequest;i.open("GET",o),i.onload=function(){document.body.insertAdjacentHTML("beforeend",i.responseText)},setTimeout(function(){i.send()},0)}else xmlhttp=new XMLHttpRequest,xmlhttp.onreadystatechange=function(){4==xmlhttp.readyState&&200==xmlhttp.status&&document.body.insertAdjacentHTML("beforeend",xmlhttp.responseText)},xmlhttp.open("GET",o,!0),xmlhttp.send()}(OpenPay.developMode?l._developHostname:OpenPay.sandboxMode?l._sandboxHostname:l._hostname,OpenPay.getId(),t),d()}l._hostname="https://api.openpay.co/",l._sandboxHostname="https://sandbox-api.openpay.co/",l._developHostname="https://dev-api.openpay.co/",l._deviceDataId=undefined,l.setup=function(t,e){var n=d();if(t&&document.getElementById(t)){var o=document.createElement("input");o.setAttribute("type","hidden"),o.value=n,o.name=e||"deviceDataId",o.id=e||"deviceDataId",document.getElementById(t).appendChild(o)}var i=!0;try{OpenPay.getId(),OpenPay.getApiKey()}catch(h){i=!1}if(i){console.log("executing sift mode");var r,s=OpenPay.getId(),a=OpenPay.getApiKey(),c=(r=OpenPay.developMode?l._developHostname:OpenPay.sandboxMode?l._sandboxHostname:l._hostname)+"v1/"+s+"/antifraudkeys";try{OpenPay.getBeaconKey.beaconKey(c,a,"",n,r)}catch(h){console.log("continue without beaconkey"+h)}}return u()},e.beaconKey=function(t,e,o,i,r){var s=null,a=null,n=t,c={},h=btoa(e+":"),l={};function d(t,e,n){clearTimeout(a);n=n||"{}";try{JSON.parse(n)}catch(o){"Response error"}}if("undefined"!=typeof JSON){4e4;var u=XMLHttpRequest&&"withCredentials"in new XMLHttpRequest;if(u){function p(){if("undefined"!=typeof s.readyState&&4==s.readyState||!u)if(clearTimeout(a),s.status<200||300<=s.status)d(0,s.status,s.responseText);else try{var t=s.responseText;t=(t=t.replace("(","")).replace(")","");var e=JSON.parse(t).data;console.log("beaconKey ok"),0<e.length?OpenPay.deviceDataSC.setupSC(o,i,e,r):console.log("Empty beaconKey normal in Sandbox")}catch(n){d(0,s.status,"{}")}}if(!(s=function y(){if(function o(t,e){var n=typeof t[e];return"function"==n||!("object"!=n||!t[e])||"unknown"==n}(window,"XMLHttpRequest"))return new XMLHttpRequest}()))return void d();for(var f in c={Accept:"application/json","Content-Type":"application/json",Authorization:"Basic "+h},s.open("GET",n,!0),"withCredentials"in s&&(s.withCredentials=!0),c)c.hasOwnProperty(f)&&c[f]&&"setRequestHeader"in s&&s.setRequestHeader(f,c[f]);"onreadystatechange"in s?s.onreadystatechange=p:"onload"in s&&"onerror"in s&&(s.onload=p,s.onerror=d),a=setTimeout(function(){"onload"in s?s.onload=Function.prototype:s.onreadystatechange=Function.prototype,s.abort(),s=null,d()},4e4),s.send()}else{if("undefined"==typeof XDomainRequest)return void d();l.apiKey=h;var _={callbackName:"getResultData",onSuccess:function(t){if(t.error)d(0,t.httpStatus,JSON.stringify(t));else try{var e=t.data;console.log("beaconKey ok"),0<e.length?OpenPay.deviceDataSC.setupSC(o,i,e,r):console.log("Empty beaconKey normal in Sandbox")}catch(n){d(0,s.status,"{}")}},onError:d,timeout:4e4,url:n+"/jsonp",data:l};$jsonp.send(_)}}else d()},t.setupSC=function(t,e,n,o){console.log("Sift Snippet");var i=window._sift=window._sift||[];i.push(["_setAccount",n]),i.push(["_setSessionId",e]),i.push(["_trackPageview"]);var r=document.createElement("script");r.type="text/javascript",r.async=!0,r.src=o+"antifraud/sc.js";var s=document.getElementsByTagName("script")[0];s.parentNode.insertBefore(r,s)},OpenPay.deviceData=l,OpenPay.getBeaconKey=e,OpenPay.deviceDataSC=t;var b={cipher:{},hash:{},keyexchange:{},mode:{},misc:{},codec:{},exception:{corrupt:function(t){this.toString=function(){return"CORRUPT: "+this.message},this.message=t},invalid:function(t){this.toString=function(){return"INVALID: "+this.message},this.message=t},bug:function(t){this.toString=function(){return"BUG: "+this.message},this.message=t},notReady:function(t){this.toString=function(){return"NOT READY: "+this.message},this.message=t}}};"undefined"!=typeof module&&module.exports&&(module.exports=b),b.cipher.aes=function(t){this._tables[0][0][0]||this._precompute();var e,n,o,i,r,s=this._tables[0][4],a=this._tables[1],c=t.length,h=1;if(4!==c&&6!==c&&8!==c)throw new b.exception.invalid("invalid aes key size");for(this._key=[i=t.slice(0),r=[]],e=c;e<4*c+28;e++)o=i[e-1],(e%c==0||8===c&&e%c==4)&&(o=s[o>>>24]<<24^s[o>>16&255]<<16^s[o>>8&255]<<8^s[255&o],e%c==0&&(o=o<<8^o>>>24^h<<24,h=h<<1^283*(h>>7))),i[e]=i[e-c]^o;for(n=0;e;n++,e--)o=i[3&n?e:e-4],r[n]=e<=4||n<4?o:a[0][s[o>>>24]]^a[1][s[o>>16&255]]^a[2][s[o>>8&255]]^a[3][s[255&o]]},b.cipher.aes.prototype={encrypt:function(t){return this._crypt(t,0)},decrypt:function(t){return this._crypt(t,1)},_tables:[[[],[],[],[],[]],[[],[],[],[],[]]],_precompute:function(){var t,e,n,o,i,r,s,a,c=this._tables[0],h=this._tables[1],l=c[4],d=h[4],u=[],p=[];for(t=0;t<256;t++)p[(u[t]=t<<1^283*(t>>7))^t]=t;for(e=n=0;!l[e];e^=o||1,n=p[n]||1)for(r=(r=n^n<<1^n<<2^n<<3^n<<4)>>8^255&r^99,a=16843009*u[i=u[o=u[d[l[e]=r]=e]]]^65537*i^257*o^16843008*e,s=257*u[r]^16843008*r,t=0;t<4;t++)c[t][e]=s=s<<24^s>>>8,h[t][r]=a=a<<24^a>>>8;for(t=0;t<5;t++)c[t]=c[t].slice(0),h[t]=h[t].slice(0)},_crypt:function(t,e){if(4!==t.length)throw new b.exception.invalid("invalid aes block size");var n,o,i,r,s=this._key[e],a=t[0]^s[0],c=t[e?3:1]^s[1],h=t[2]^s[2],l=t[e?1:3]^s[3],d=s.length/4-2,u=4,p=[0,0,0,0],f=this._tables[e],_=f[0],y=f[1],m=f[2],g=f[3],v=f[4];for(r=0;r<d;r++)n=_[a>>>24]^y[c>>16&255]^m[h>>8&255]^g[255&l]^s[u],o=_[c>>>24]^y[h>>16&255]^m[l>>8&255]^g[255&a]^s[u+1],i=_[h>>>24]^y[l>>16&255]^m[a>>8&255]^g[255&c]^s[u+2],l=_[l>>>24]^y[a>>16&255]^m[c>>8&255]^g[255&h]^s[u+3],u+=4,a=n,c=o,h=i;for(r=0;r<4;r++)p[e?3&-r:r]=v[a>>>24]<<24^v[c>>16&255]<<16^v[h>>8&255]<<8^v[255&l]^s[u++],n=a,a=c,c=h,h=l,l=n;return p}},b.bitArray={bitSlice:function(t,e,n){return t=b.bitArray._shiftRight(t.slice(e/32),32-(31&e)).slice(1),n===undefined?t:b.bitArray.clamp(t,n-e)},extract:function(t,e,n){var o=Math.floor(-e-n&31);return(-32&(e+n-1^e)?t[e/32|0]<<32-o^t[e/32+1|0]>>>o:t[e/32|0]>>>o)&(1<<n)-1},concat:function(t,e){if(0===t.length||0===e.length)return t.concat(e);var n=t[t.length-1],o=b.bitArray.getPartial(n);return 32===o?t.concat(e):b.bitArray._shiftRight(e,o,0|n,t.slice(0,t.length-1))},bitLength:function(t){var e,n=t.length;return 0===n?0:(e=t[n-1],32*(n-1)+b.bitArray.getPartial(e))},clamp:function(t,e){if(32*t.length<e)return t;var n=(t=t.slice(0,Math.ceil(e/32))).length;return e&=31,0<n&&e&&(t[n-1]=b.bitArray.partial(e,t[n-1]&2147483648>>e-1,1)),t},partial:function(t,e,n){return 32===t?e:(n?0|e:e<<32-t)+1099511627776*t},getPartial:function(t){return Math.round(t/1099511627776)||32},equal:function(t,e){if(b.bitArray.bitLength(t)!==b.bitArray.bitLength(e))return!1;var n,o=0;for(n=0;n<t.length;n++)o|=t[n]^e[n];return 0===o},_shiftRight:function(t,e,n,o){var i,r,s;for(o===undefined&&(o=[]);32<=e;e-=32)o.push(n),n=0;if(0===e)return o.concat(t);for(i=0;i<t.length;i++)o.push(n|t[i]>>>e),n=t[i]<<32-e;return r=t.length?t[t.length-1]:0,s=b.bitArray.getPartial(r),o.push(b.bitArray.partial(e+s&31,32<e+s?n:o.pop(),1)),o},_xor4:function(t,e){return[t[0]^e[0],t[1]^e[1],t[2]^e[2],t[3]^e[3]]}},b.codec.utf8String={fromBits:function(t){var e,n,o="",i=b.bitArray.bitLength(t);for(e=0;e<i/8;e++)0==(3&e)&&(n=t[e/4]),o+=String.fromCharCode(n>>>24),n<<=8;return decodeURIComponent(escape(o))},toBits:function(t){t=unescape(encodeURIComponent(t));var e,n=[],o=0;for(e=0;e<t.length;e++)o=o<<8|t.charCodeAt(e),3==(3&e)&&(n.push(o),o=0);return 3&e&&n.push(b.bitArray.partial(8*(3&e),o)),n}},b.codec.base64={_chars:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",fromBits:function(t,e,n){var o,i="",r=0,s=b.codec.base64._chars,a=0,c=b.bitArray.bitLength(t);for(n&&(s=s.substr(0,62)+"-_"),o=0;6*i.length<c;)i+=s.charAt((a^t[o]>>>r)>>>26),r<6?(a=t[o]<<6-r,r+=26,o++):(a<<=6,r-=6);for(;3&i.length&&!e;)i+="=";return i},toBits:function(t,e){t=t.replace(/\s|=/g,"");var n,o,i=[],r=0,s=b.codec.base64._chars,a=0;for(e&&(s=s.substr(0,62)+"-_"),n=0;n<t.length;n++){if((o=s.indexOf(t.charAt(n)))<0)throw new b.exception.invalid("this isn't base64!");26<r?(r-=26,i.push(a^o>>>r),a=o<<32-r):a^=o<<32-(r+=6)}return 56&r&&i.push(b.bitArray.partial(56&r,a,1)),i}},b.codec.base64url={fromBits:function(t){return b.codec.base64.fromBits(t,1,1)},toBits:function(t){return b.codec.base64.toBits(t,1)}},b.hash.sha256=function(t){this._key[0]||this._precompute(),t?(this._h=t._h.slice(0),this._buffer=t._buffer.slice(0),this._length=t._length):this.reset()},b.hash.sha256.hash=function(t){return(new b.hash.sha256).update(t).finalize()},b.hash.sha256.prototype={blockSize:512,reset:function(){return this._h=this._init.slice(0),this._buffer=[],this._length=0,this},update:function(t){"string"==typeof t&&(t=b.codec.utf8String.toBits(t));var e,n=this._buffer=b.bitArray.concat(this._buffer,t),o=this._length,i=this._length=o+b.bitArray.bitLength(t);for(e=512+o&-512;e<=i;e+=512)this._block(n.splice(0,16));return this},finalize:function(){var t,e=this._buffer,n=this._h;for(t=(e=b.bitArray.concat(e,[b.bitArray.partial(1,1)])).length+2;15&t;t++)e.push(0);for(e.push(Math.floor(this._length/4294967296)),e.push(0|this._length);e.length;)this._block(e.splice(0,16));return this.reset(),n},_init:[],_key:[],_precompute:function(){var t,e=0,n=2;function o(t){return 4294967296*(t-Math.floor(t))|0}t:for(;e<64;n++){for(t=2;t*t<=n;t++)if(n%t==0)continue t;e<8&&(this._init[e]=o(Math.pow(n,.5))),this._key[e]=o(Math.pow(n,1/3)),e++}},_block:function(t){var e,n,o,i,r=t.slice(0),s=this._h,a=this._key,c=s[0],h=s[1],l=s[2],d=s[3],u=s[4],p=s[5],f=s[6],_=s[7];for(e=0;e<64;e++)n=(n=e<16?r[e]:(o=r[e+1&15],i=r[e+14&15],r[15&e]=(o>>>7^o>>>18^o>>>3^o<<25^o<<14)+(i>>>17^i>>>19^i>>>10^i<<15^i<<13)+r[15&e]+r[e+9&15]|0))+_+(u>>>6^u>>>11^u>>>25^u<<26^u<<21^u<<7)+(f^u&(p^f))+a[e],_=f,f=p,p=u,u=d+n|0,d=l,l=h,c=n+((h=c)&l^d&(h^l))+(h>>>2^h>>>13^h>>>22^h<<30^h<<19^h<<10)|0;s[0]=s[0]+c|0,s[1]=s[1]+h|0,s[2]=s[2]+l|0,s[3]=s[3]+d|0,s[4]=s[4]+u|0,s[5]=s[5]+p|0,s[6]=s[6]+f|0,s[7]=s[7]+_|0}},b.prng=function(t){this._pools=[new b.hash.sha256],this._poolEntropy=[0],this._reseedCount=0,this._robins={},this._eventId=0,this._collectorIds={},this._collectorIdNext=0,this._strength=0,this._poolStrength=0,this._nextReseed=0,this._key=[0,0,0,0,0,0,0,0],this._counter=[0,0,0,0],this._cipher=undefined,this._defaultParanoia=t,this._collectorsStarted=!1,this._callbacks={progress:{},seeded:{}},this._callbackI=0,this._NOT_READY=0,this._READY=1,this._REQUIRES_RESEED=2,this._MAX_WORDS_PER_BURST=65536,this._PARANOIA_LEVELS=[0,48,64,96,128,192,256,384,512,768,1024],this._MILLISECONDS_PER_RESEED=3e4,this._BITS_PER_RESEED=80},b.prng.prototype={randomWords:function(t,e){var n,o,i=[],r=this.isReady(e);if(r===this._NOT_READY)throw new b.exception.notReady("generator isn't seeded");for(r&this._REQUIRES_RESEED&&this._reseedFromPools(!(r&this._READY)),n=0;n<t;n+=4)(n+1)%this._MAX_WORDS_PER_BURST==0&&this._gate(),o=this._gen4words(),i.push(o[0],o[1],o[2],o[3]);return this._gate(),i.slice(0,t)},setDefaultParanoia:function(t,e){if(0===t&&"Setting paranoia=0 will ruin your security; use it only for testing"!==e)throw"Setting paranoia=0 will ruin your security; use it only for testing";this._defaultParanoia=t},addEntropy:function(t,e,n){n=n||"user";var o,i,r,s,a=(new Date).valueOf(),c=this._robins[n],h=this.isReady(),l=0;switch((o=this._collectorIds[n])===undefined&&(o=this._collectorIds[n]=this._collectorIdNext++),c===undefined&&(c=this._robins[n]=0),this._robins[n]=(this._robins[n]+1)%this._pools.length,typeof t){case"number":e===undefined&&(e=1),this._pools[c].update([o,this._eventId++,1,e,a,1,0|t]);break;case"object":if("[object Uint32Array]"===(s=Object.prototype.toString.call(t))){for(r=[],i=0;i<t.length;i++)r.push(t[i]);t=r}else for("[object Array]"!==s&&(l=1),i=0;i<t.length&&!l;i++)"number"!=typeof t[i]&&(l=1);if(!l){if(e===undefined)for(i=e=0;i<t.length;i++)for(r=t[i];0<r;)e++,r>>>=1;this._pools[c].update([o,this._eventId++,2,e,a,t.length].concat(t))}break;case"string":e===undefined&&(e=t.length),this._pools[c].update([o,this._eventId++,3,e,a,t.length]),this._pools[c].update(t);break;default:l=1}if(l)throw new b.exception.bug("random: addEntropy only supports number, array of numbers or string");this._poolEntropy[c]+=e,this._poolStrength+=e,h===this._NOT_READY&&(this.isReady()!==this._NOT_READY&&this._fireEvent("seeded",Math.max(this._strength,this._poolStrength)),this._fireEvent("progress",this.getProgress()))},isReady:function(t){var e=this._PARANOIA_LEVELS[t!==undefined?t:this._defaultParanoia];return this._strength&&this._strength>=e?this._poolEntropy[0]>this._BITS_PER_RESEED&&(new Date).valueOf()>this._nextReseed?this._REQUIRES_RESEED|this._READY:this._READY:this._poolStrength>=e?this._REQUIRES_RESEED|this._NOT_READY:this._NOT_READY},getProgress:function(t){var e=this._PARANOIA_LEVELS[t||this._defaultParanoia];return this._strength>=e?1:this._poolStrength>e?1:this._poolStrength/e},startCollectors:function(){if(!this._collectorsStarted){if(this._eventListener={loadTimeCollector:this._bind(this._loadTimeCollector),mouseCollector:this._bind(this._mouseCollector),accelerometerCollector:this._bind(this._accelerometerCollector)},window.addEventListener)window.addEventListener("load",this._eventListener.loadTimeCollector,!1),window.addEventListener("mousemove",this._eventListener.mouseCollector,!1),window.addEventListener("devicemotion",this._eventListener.accelerometerCollector,!1);else{if(!document.attachEvent)throw new b.exception.bug("can't attach event");document.attachEvent("onload",this._eventListener.loadTimeCollector),document.attachEvent("onmousemove",this._eventListener.mouseCollector)}this._collectorsStarted=!0}},stopCollectors:function(){this._collectorsStarted&&(window.removeEventListener?(window.removeEventListener("load",this._eventListener.loadTimeCollector,!1),window.removeEventListener("mousemove",this._eventListener.mouseCollector,!1),window.removeEventListener("devicemotion",this._eventListener.accelerometerCollector,!1)):document.detachEvent&&(document.detachEvent("onload",this._eventListener.loadTimeCollector),document.detachEvent("onmousemove",this._eventListener.mouseCollector)),this._collectorsStarted=!1)},addEventListener:function(t,e){this._callbacks[t][this._callbackI++]=e},removeEventListener:function(t,e){var n,o,i=this._callbacks[t],r=[];for(o in i)i.hasOwnProperty(o)&&i[o]===e&&r.push(o);for(n=0;n<r.length;n++)delete i[o=r[n]]},_bind:function(t){var e=this;return function(){t.apply(e,arguments)}},_gen4words:function(){for(var t=0;t<4&&(this._counter[t]=this._counter[t]+1|0,!this._counter[t]);t++);return this._cipher.encrypt(this._counter)},_gate:function(){this._key=this._gen4words().concat(this._gen4words()),this._cipher=new b.cipher.aes(this._key)},_reseed:function(t){this._key=b.hash.sha256.hash(this._key.concat(t)),this._cipher=new b.cipher.aes(this._key);for(var e=0;e<4&&(this._counter[e]=this._counter[e]+1|0,!this._counter[e]);e++);},_reseedFromPools:function(t){var e,n=[],o=0;for(this._nextReseed=n[0]=(new Date).valueOf()+this._MILLISECONDS_PER_RESEED,e=0;e<16;e++)n.push(4294967296*Math.random()|0);for(e=0;e<this._pools.length&&(n=n.concat(this._pools[e].finalize()),o+=this._poolEntropy[e],this._poolEntropy[e]=0,t||!(this._reseedCount&1<<e));e++);this._reseedCount>=1<<this._pools.length&&(this._pools.push(new b.hash.sha256),this._poolEntropy.push(0)),this._poolStrength-=o,o>this._strength&&(this._strength=o),this._reseedCount++,this._reseed(n)},_mouseCollector:function(t){var e=t.x||t.clientX||t.offsetX||0,n=t.y||t.clientY||t.offsetY||0;b.random.addEntropy([e,n],2,"mouse"),this._addCurrentTimeToEntropy(0)},_loadTimeCollector:function(){this._addCurrentTimeToEntropy(2)},_addCurrentTimeToEntropy:function(t){window&&window.performance&&"function"==typeof window.performance.now?b.random.addEntropy(window.performance.now(),t,"loadtime"):b.random.addEntropy((new Date).valueOf(),t,"loadtime")},_accelerometerCollector:function(t){var e=t.accelerationIncludingGravity.x||t.accelerationIncludingGravity.y||t.accelerationIncludingGravity.z;if(window.orientation){var n=window.orientation;"number"==typeof n&&b.random.addEntropy(n,1,"accelerometer")}e&&b.random.addEntropy(e,2,"accelerometer"),this._addCurrentTimeToEntropy(0)},_fireEvent:function(t,e){var n,o=b.random._callbacks[t],i=[];for(n in o)o.hasOwnProperty(n)&&i.push(o[n]);for(n=0;n<i.length;n++)i[n](e)}},b.random=new b.prng(6),function(){try{var t,e,n;if("undefined"!=typeof module&&module.exports&&(e=require("crypto"))&&e.randomBytes)t=e.randomBytes(128),t=new Uint32Array(new Uint8Array(t).buffer),b.random.addEntropy(t,1024,"crypto.randomBytes");else if(window&&Uint32Array){if(n=new Uint32Array(32),window.crypto&&window.crypto.getRandomValues)window.crypto.getRandomValues(n);else{if(!window.msCrypto||!window.msCrypto.getRandomValues)return;window.msCrypto.getRandomValues(n)}b.random.addEntropy(n,1024,"crypto.getRandomValues")}}catch(o){console.log("There was an error collecting entropy from the browser:"),console.log(o)}}()}).call(this);
