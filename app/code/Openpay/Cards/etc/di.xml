<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="../../../../../lib/internal/Magento/Framework/ObjectManager/etc/config.xsd">
    <type name="Openpay\Cards\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="Openpay\Cards\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">openpayCardsLogger</argument>
            <argument name="handlers"  xsi:type="array">
                <item name="system" xsi:type="object">Openpay\Cards\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
</config>

