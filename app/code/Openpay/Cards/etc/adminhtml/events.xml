<?xml version="1.0"?>
<!--
/**
 * Openpay_Cards events configuration
 *
 * @category    Openpay
 * @package     Openpay_Cards
 * <AUTHOR>
 * @copyright   Openpay (http://openpay.mx)
 * @license     http://www.apache.org/licenses/LICENSE-2.0  Apache License Version 2.0
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="admin_system_config_changed_section_payment">
        <observer name="openpaycards_observer_check_config" instance="Openpay\Cards\Observer\MerchantInfo"/>
        <observer name="openpaycards_observer_check_order_status" instance="Openpay\Cards\Observer\CreateWebhook"/>
    </event>
</config>
