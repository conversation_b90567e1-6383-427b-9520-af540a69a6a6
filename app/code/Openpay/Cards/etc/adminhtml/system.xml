<?xml version="1.0"?>
<!--
/**
 * Openpay_Cards admin configuration
 *
 * @category    Openpay
 * @package     Openpay_Cards
 * <AUTHOR>
 * @copyright   Openpay (http://openpay.mx)
 * @license     http://www.apache.org/licenses/LICENSE-2.0  Apache License Version 2.0
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="payment">
            <group id="openpay_cards" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Openpay (Tarjetas)</label>
                <comment>
                    <![CDATA[
                    <p>Version: 3.5.12</p>
                    <a href="http://openpay.mx/" target="_blank">Clic aquí para registrar una cuenta con Openpay</a>
                    ]]>
                </comment>
                <field id="active" translate="label" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Habilitar</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="is_sandbox" translate="label" type="select" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Sandbox</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="country" translate="label" type="select" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>País</label>
                    <source_model>Openpay\Cards\Model\Source\Countries::getOptions</source_model>
                </field>
                <field id="title" translate="label" type="text" sortOrder="6" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="0">
                    <label>Título</label>
                </field>
                <field id="sandbox_merchant_id" translate="label" type="text" sortOrder="7" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Sandbox Merchant ID</label>
                    <depends>
                        <field id="is_sandbox">1</field>
                    </depends>
                </field>
                <field id="sandbox_sk" translate="label" type="obscure" sortOrder="8" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Sandbox Llave Secreta</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="is_sandbox">1</field>
                    </depends>
                </field>
                <field id="sandbox_pk" translate="label" type="text" sortOrder="9" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Sandbox Llave Pública</label>
                    <depends>
                        <field id="is_sandbox">1</field>
                    </depends>
                </field>
                <field id="live_merchant_id" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Producción Merchant ID</label>
                    <depends>
                        <field id="is_sandbox">0</field>
                    </depends>
                </field>
                <field id="live_sk" translate="label" type="obscure" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Producción Llave Secreta</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="is_sandbox">0</field>
                    </depends>
                </field>
                <field id="live_pk" translate="label" type="text" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Producción Llave Pública</label>
                    <depends>
                        <field id="is_sandbox">0</field>
                    </depends>
                </field>
                <field id="charge_type_mx" translate="label" type="select" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>¿Cómo procesar el cargo?</label>
                    <source_model>Openpay\Cards\Model\Source\ChargeTypes::getTypes</source_model>
                    <depends>
                        <field id="country">MX</field>
                        <field id="merchant_classification">Openpay</field>
                    </depends>
                    <comment>¿Qué es la autenticación selectiva? Es cuando se detecta riesgo en la transacción y se manda por 3D Secure.</comment>
                </field>
                <field id="charge_type_co_pe" translate="label" type="select" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>¿Cómo procesar el cargo?</label>
                    <source_model>Openpay\Cards\Model\Source\ChargeTypes::getTypesCoPe</source_model>
                    <depends>
                        <field id="country" separator="|">CO|PE</field>
                        <field id="merchant_classification">Openpay</field>
                    </depends>
                </field>
                <field id="payment_action" translate="label" type="select" sortOrder="14" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Configuración del cargo</label>
                    <source_model>Openpay\Cards\Model\Source\PaymentAction</source_model>
                    <depends>
                        <field id="country" separator="|">MX|PE</field>
                        <field id="merchant_classification">Openpay</field>
                    </depends>
                    <comment>Indica si el cargo se hace o no inmediatamente, con la pre-autorización solo se reserva el monto para ser confirmado o cancelado posteriormente. Las pre-autorizaciones no pueden ser utilizadas en combinación con pago con puntos Bancomer.</comment>
                </field>
                <field id="use_card_points" translate="label" type="select" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Pago con puntos</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="country">MX</field>
                    </depends>
                    <comment>Recibe pagos con puntos Bancomer habilitando esta opción. Esta opción no se puede combinar con pre-autorizaciones o MSI.</comment>
                </field>
                <field id="save_cc_mx" translate="label" type="select" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Guardar tarjetas</label>
                    <source_model>Openpay\Cards\Model\Source\SaveCc::getSaveCcMxCo</source_model>
                    <depends>
                        <field id="country">MX</field>
                    </depends>
                    <comment>Permite a los usuarios guardar sus tarjetas de crédito para agilizar sus futuras compras.</comment>
                </field>
                <field id="save_cc_co" translate="label" type="select" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Guardar tarjetas</label>
                    <source_model>Openpay\Cards\Model\Source\SaveCc::getSaveCcMxCo</source_model>
                    <depends>
                        <field id="country">CO</field>
                    </depends>
                    <comment>Permite a los usuarios guardar sus tarjetas de crédito para agilizar sus futuras compras.</comment>
                </field>
                <field id="save_cc_pe" translate="label" type="select" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Guardar tarjetas</label>
                    <tooltip>La opción "Guardar y no solicitar CVV" requiere una configuración adicional de Openpay,
                            contacte a nuestro equipo de soporte para activarlo </tooltip>
                    <source_model>Openpay\Cards\Model\Source\SaveCc::getSaveCc</source_model>
                    <depends>
                        <field id="country">PE</field>
                    </depends>
                    <comment>Permite a los usuarios guardar sus tarjetas de crédito para agilizar sus futuras compras.</comment>
                </field>

                <field id="cctypes_mx" translate="label" type="multiselect" sortOrder="17" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Tipos de Tarjetas</label>
                    <source_model>Openpay\Cards\Model\Source\Cctype::getAllowedTypesMx</source_model>
                    <depends>
                        <field id="country">MX</field>
                    </depends>
                    <comment>Presione ctrl y clic para seleccionar más de una opción</comment>
                </field>
                <field id="cctypes_co" translate="label" type="multiselect" sortOrder="18" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Tipos de Tarjetas</label>
                    <source_model>Openpay\Cards\Model\Source\Cctype::getAllowedTypesCo</source_model>
                    <depends>
                        <field id="country">CO</field>
                    </depends>
                    <comment>Presione ctrl y clic para seleccionar más de una opción</comment>
                </field>
                <field id="cctypes_pe" translate="label" type="multiselect" sortOrder="19" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Tipos de Tarjetas</label>
                    <source_model>Openpay\Cards\Model\Source\Cctype::getAllowedTypesPe</source_model>
                    <depends>
                        <field id="country">PE</field>
                    </depends>
                    <comment>Presione ctrl y clic para seleccionar más de una opción</comment>
                </field>
                <field id="interest_free" translate="label" type="multiselect" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Meses sin Intereses</label>
                    <source_model>Openpay\Cards\Model\Source\MonthsInterestFree::getMonths</source_model>
                    <depends>
                        <field id="country">MX</field>
                    </depends>
                    <comment>Presione ctrl y clic para seleccionar más de una opción, esta opción no se puede combinar con pago con puntos. Recomendamos revises la documentación de los MSI (https://www.openpay.mx/costos.html).</comment>
                </field>
                <field id="minimum_amounts" translate="label" type="select" sortOrder="21" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Configurar Montos Mínimos MSI</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Configuración de montos mínimos para promociones a MSI</comment>
                    <depends>
                        <field id="country">MX</field>
                    </depends>
                </field>
                <field id="installments" translate="label" type="select" sortOrder="22" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Configurar Cuotas</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Configuración para activar cuotas a sus clientes</comment>
                    <depends>
                        <field id="country">PE</field>
                    </depends>
                </field>
                <field id="three_months" translate="label" type="text" sortOrder="23" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>3 Meses</label>
                    <validate>required-entry validate-digits validate-digits-range digits-range-300-99999</validate>
                    <depends>
                        <field id="country">MX</field>
                        <field id="minimum_amounts">1</field>
                    </depends>
                    <comment>Monto mínimo por default $300. El monto deberá ser mayor al asignado por default</comment>
                </field>
                <field id="six_months" translate="label" type="text" sortOrder="24" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>6 Meses</label>
                    <validate>required-entry validate-digits validate-digits-range digits-range-600-99999</validate>
                    <depends>
                        <field id="country">MX</field>
                        <field id="minimum_amounts">1</field>
                    </depends>
                    <comment>Monto mínimo por default $600. El monto deberá ser mayor al asignado por default</comment>
                </field>
                <field id="nine_months" translate="label" type="text" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>9 Meses</label>
                    <validate>required-entry validate-digits validate-digits-range digits-range-900-99999</validate>
                    <depends>
                        <field id="country">MX</field>
                        <field id="minimum_amounts">1</field>
                    </depends>
                    <comment>Monto mínimo por default $900. El monto deberá ser mayor al asignado por default</comment>
                </field>
                <field id="twelve_months" translate="label" type="text" sortOrder="26" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>12 Meses</label>
                    <validate>required-entry validate-digits validate-digits-range digits-range-1200-99999</validate>
                    <depends>
                        <field id="country">MX</field>
                        <field id="minimum_amounts">1</field>
                    </depends>
                    <comment>Monto mínimo por default $1200. El monto deberá ser mayor al asignado por default</comment>
                </field>
                <field id="eighteen_months" translate="label" type="text" sortOrder="27" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>18 Meses</label>
                    <validate>required-entry validate-digits validate-digits-range digits-range-1800-99999</validate>
                    <depends>
                        <field id="country">MX</field>
                        <field id="minimum_amounts">1</field>
                    </depends>
                    <comment>Monto mínimo por default $1800. El monto deberá ser mayor al asignado por default</comment>
                </field>
                <field id="iva" translate="label" type="text" sortOrder="28" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>IVA</label>
                    <validate>required-entry validate-zero-or-greater validate-digits</validate>
                    <comment>Debe contener el valor de IVA, es campo solo informativo, no tiene ningún efecto sobre el campo amount.</comment>
                    <depends>
                        <field id="country">CO</field>
                    </depends>
                </field>
                <field id="allowspecific" translate="label" type="allowspecific" sortOrder="29" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Pago aplicable para países</label>
                    <source_model>Magento\Payment\Model\Config\Source\Allspecificcountries</source_model>
                </field>
                <field id="specificcountry" translate="label" type="multiselect" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Pago para países específicos</label>
                    <source_model>Magento\Directory\Model\Config\Source\Country</source_model>
                </field>
                <field id="processing_openpay" translate="label" type="select" sortOrder="31" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Openpay processing</label>
                    <source_model>Openpay\Cards\Model\Config\OrderStatuses</source_model>
                    <comment>Estatus que tendrá la orden una vez que el módulo de tarjetas lo establezca en "processing"</comment>
                </field>
                <field id="pending_payment_openpay" translate="label" type="select" sortOrder="32" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Openpay Pendiente de Pago</label>
                    <source_model>Openpay\Cards\Model\Config\OrderStatuses</source_model>
                    <comment>Estatus que tendrá la orden una vez que el módulo de tarjetas lo establezca en "payment_review"</comment>
                </field>
                <field id="canceled_openpay" translate="label" type="select" sortOrder="33" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Openpay Cancelado</label>
                    <source_model>Openpay\Cards\Model\Config\OrderStatuses</source_model>
                    <comment>Estatus que tendrá la orden una vez que el módulo de tarjetas lo establezca en "canceled"</comment>
                </field>
                <field id="sort_order" translate="label" type="text" sortOrder="34" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Orden</label>
                </field>
            </group>
        </section>
    </system>
</config>
