<?xml version="1.0"?>
<csp_whitelist xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Csp:etc/csp_whitelist.xsd">
    <policies>
         <policy id="script-src">
            <values>
                <value id="openpay-lib.mx" type="host">*.s3.amazonaws.com</value>
                <value id="openpay-lib.co" type="host">*.openpay.co</value>
                <value id="openpay-lib.pe" type="host">*.openpay.pe</value>
                <value id="analytics-google" type="host">*.google-analytics.com</value>
                <value id="recaptcha-google" type="host">*.google.com/recaptcha/</value>
                <value id="recaptcha-gstatic" type="host">*.gstatic.com/recaptcha/</value>
            </values>
        </policy>
        <policy id="connect-src">
            <values>
                <value id="openpay-mx1" type="host">*.openpay.mx</value>
                <value id="openpay-co1" type="host">*.openpay.co</value>
                <value id="openpay-pe1" type="host">*.openpay.pe</value>
            </values>
        </policy>
         <policy id="frame-src">
            <values>
                <value id="openpay-paynet" type="host">*.opencontrol.mx</value>
                <value id="kaptcha" type="host">*.kaptcha.com</value>
                <value id="openpayapi-pe" type="host">*.openpay.pe</value>
            </values>
        </policy>
    </policies>
</csp_whitelist>
