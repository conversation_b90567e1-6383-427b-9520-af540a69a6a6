<?php

namespace Converse\Swatches\Plugin;

use Magento\Swatches\Helper\Data as SwatchData;
use Magento\Framework\Json\EncoderInterface;
use Magento\Framework\Json\DecoderInterface;

/**
 * SwatchConfiguration plugin ensures that visual swatches
 * returned as images alw
 */
class SwatchConfiguration {

    private $swatchHelper;
    private $logger;
    private $jsonEncoder;
    private $jsonDecoder;


    public function __construct(EncoderInterface $jsonEncoder, DecoderInterface $jsonDecoder, SwatchData $swatchHelper, \Psr\Log\LoggerInterface $logger) {
        $this->swatchHelper = $swatchHelper;
        $this->logger = $logger;
        $this->jsonEncoder = $jsonEncoder;
        $this->jsonDecoder = $jsonDecoder;
    }

    /**
     * Return unmolested color information with visual swatches in image thumbnail mode
     */
    public function afterGetJsonSwatchConfig(\Magento\Swatches\Block\Product\Renderer\Listing\Configurable $subject, $result) {
        $attributesData = $this->swatchHelper->getSwatchAttributesAsArray($subject->getProduct());
        $optionIds = [];
        // get all option ids
        foreach ($attributesData as $attributeId => $attributeDataArray) {
            if (isset($attributeDataArray['options'])) {
                foreach ($attributeDataArray['options'] as $optionId => $label) {
                    $optionIds[] = $optionId;
                }
            }
        }
        // get all swatch data
        $swatchesData = $this->swatchHelper->getSwatchesByOptionsId($optionIds);
        // append swatch data to result
        $config = $this->jsonDecoder->decode($result);
        foreach ($config as $attributeId => $attributeDataArray) {
            foreach($attributeDataArray as $optionId => $option) {
                if (isset($option['type']) && $option['type'] === 2) {
                    $config[$attributeId][$optionId]['hexcolor'] = $swatchesData[$optionId]['value'];
                }
            }
        }
        
        // enable for debugging
        // $this->logger->warn("plugin SwatchConfiguration");
        // $this->logger->warn($this->jsonEncoder->encode($config));

        return $this->jsonEncoder->encode($config);
    }
}