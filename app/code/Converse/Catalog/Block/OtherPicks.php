<?php

/**
 * Class Catalog
 *
 * PHP version 7
 *
 * @category Converse
 * @package  Converse_Catalog

 */

namespace Converse\Catalog\Block;

class OtherPicks extends \Magento\Catalog\Block\Product\AbstractProduct {

    protected $_objectManager = null;
    protected $_categoryFactory;
    protected $_category;
    protected $_product;
    protected $_productCollectionFactory;

    public function __construct(
        \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
        \Magento\Catalog\Block\Product\Context $context,
        \Magento\Framework\ObjectManagerInterface $objectManager,
        \Magento\Catalog\Model\CategoryFactory $categoryFactory
    ) {
        $this->_productCollectionFactory = $productCollectionFactory;
        $this->_objectManager = $objectManager;
        $this->_categoryFactory = $categoryFactory;
        parent::__construct($context);
    }

    public function getCurrentCategory()
    {
        $this->_category = $this->_objectManager->get('Magento\Framework\Registry')->registry('current_category');
        return $this->_category;
    }

    public function getCurrentProduct()
    {
        $this->_product = $this->_objectManager->get('Magento\Framework\Registry')->registry('current_product');
        return $this->_product;
    }

    /**
     * Get category object
     *
     * @return \Magento\Catalog\Model\Category
     */
    public function getCategory($categoryId)
    {
        $this->_category = $this->_categoryFactory->create();
        $this->_category->load($categoryId);
        return $this->_category;
    }

    /**
     * Get all children categories IDs
     *
     * @param boolean $asArray return result as array instead of comma-separated list of IDs
     * @return array|string
     */
    public function getAllChildren($asArray = false, $categoryId = false)
    {
        if ($this->_category) {
            return $this->_category->getAllChildren($asArray);
        } else {
            return $this->getCategory($categoryId)->getAllChildren($asArray);
        }
    }

    protected function getCategoriesIds($categories)
    {
        $ids = [];
        foreach ($categories as $category) {
            $ids[] = $category->getId();
        }
        return $ids;
    }

    public function getProductCollection()
    {
        $collection = $this->_productCollectionFactory->create();
        $collection->addAttributeToSelect('*');
        $category = $this->getCurrentCategory();
        $product = $this->getCurrentProduct();
        $attributes = $product->getAttributes();
        $attributeCodes = array_keys($attributes);
        //if current category is null, add de categories from product in filter.
        if ($category) {
            $categoryIds = $this->getAllChildren(true);
        } else {
            $categoryIds = $product->getCategoryIds();
        }
        $collection->addCategoriesFilter(['in' => $categoryIds]);
        // don't display the current product in category recs
        if ($product) {
            $collection->addAttributeToFilter('entity_id', array("neq" => $product->getId()));
        }
        // filter by is_recommended if present
        if (in_array("is_recommended", $attributeCodes)) {
            $collection->addAttributeToFilter('is_recommended', array("eq" => true));
        }
        $collection->addAttributeToFilter('visibility', \Magento\Catalog\Model\Product\Visibility::VISIBILITY_BOTH);
        $collection->addAttributeToFilter('status',\Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED);
        $collection->setPageSize(9); // fetching only 9 products
        return $collection;
    }
}

