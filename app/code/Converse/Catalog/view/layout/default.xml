<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page layout="1column"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="content">
            <block class="Converse\Catalog\Block\OtherPicks" name="product.view.otherpicks" after="product.info.details" template="Converse_Catalog::otherpicks.phtml" />
        </referenceContainer>
    </body>
</page>