Banner Extension for Magneto 2 developed by <PERSON><PERSON>

Welcome to Banner Slider Extension for Magneto 2 developed by Converse.

The extension lets the store owners place smart banners on their website.

##Support:
version - 2.3.x, 2.4.x

##How to install Extension

1. Get the extension’s version number
   Note: Refer to the below link to get the version number of the downloaded extension.
   https://devdocs.magento.com/extensions/install/#get-the-extensions-composer-name-and-version
2. Navigate to your Magento project directory and update your composer.json file
   Command: composer require converse/magento-2-banner-extension:<version>

#Enable Extension:

- php bin/magento module:enable Converse_Banner
- php bin/magento setup:install
- php bin/magento setup:upgrade
- php bin/magento setup:di:compile
- php bin/magento setup:static-content:deploy
- php bin/magento cache:flush

#Disable Extension:

- php bin/magento module:disable Converse_Banner
- php bin/magento setup:upgrade
- php bin/magento setup:di:compile
- php bin/magento setup:static-content:deploy
- php bin/magento cache:flush
