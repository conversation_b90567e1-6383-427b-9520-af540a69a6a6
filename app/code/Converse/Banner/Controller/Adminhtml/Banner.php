<?php
/**
 * Class Banner
 *
 * PHP version 7
 *
 * @category Converse
 * @package  Converse_Banner

 */
namespace Converse\Banner\Controller\Adminhtml;

/**
 * Class Banner
 *
 * @category Converse
 * @package  Converse_Banner

 */
class Banner extends Actions
{
    /**
     * Form session key
     *
     * @var string
     */
    protected $formSessionKey = 'converse_banner_form_data';

    /**
     * Allowed Key
     *
     * @var string
     */
    protected $allowedKey = 'Converse_Banner::manage_banners';

    /**
     * Model class name
     *
     * @var string
     */
    protected $modelClass = \Converse\Banner\Model\Banner::class;

    /**
     * Active menu key
     *
     * @var string
     */
    protected $activeMenu = 'Converse_Banner::banner';

    /**
     * Status field name
     *
     * @var string
     */
    protected $statusField = 'is_active';

    /**
     * Save request params key
     *
     * @var string
     */
    protected $paramsHolder = 'post';
}
