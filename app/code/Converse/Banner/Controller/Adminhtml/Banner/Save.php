<?php
/**
 * Class save
 *
 * PHP version 7
 *
 * @category Converse
 * @package  Converse_Banner

 */
namespace Converse\Banner\Controller\Adminhtml\Banner;

use Converse\Banner\Model\Banner;
use Magento\Framework\Filesystem;
use Magento\MediaStorage\Model\File\UploaderFactory;

/**
 * Class save
 *
 * @category Converse
 * @package  Converse_Banner

 */
class Save extends \Converse\Banner\Controller\Adminhtml\Banner
{

    /**
     * Save constructor.
     *
     * @param \Magento\Backend\App\Action\Context           $context         context
     * @param Filesystem                                    $fileSystem      fileSystem
     * @param UploaderFactory                               $uploaderfactory uploaderfactory
     * @param \Converse\Banner\Model\BannerFactory        $bannerFactory   bannerFactory
     * @param \Converse\Banner\Model\ResourceModel\Banner $bannerResource  bannerResource
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Converse\Banner\Model\BannerFactory $bannerFactory,
        \Converse\Banner\Model\ResourceModel\Banner $bannerResource,
        \Converse\Banner\Model\ResourceModel\Banner\CollectionFactory $bannerCollectionFactory
    ) {
        parent::__construct($context, $bannerFactory, $bannerResource, $bannerCollectionFactory);
    }

    /**
     * Before save method
     *
     * @param \Converse\Banner\Model\Banner         $model   model
     * @param \Magento\Framework\App\RequestInterface $request request
     *
     * @return bool|void
     */
    protected function _beforeSave($model, $request)
    {
    }
}
