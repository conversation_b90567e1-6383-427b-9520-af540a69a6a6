<?php
/**
 * Copyright (c) 2025 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> <<EMAIL>> | Omnipro Team
 * @date 17/2/2025
 * @category Omnipro
 */

declare(strict_types=1);

namespace Omnipro\ProductDetailExport\Model\Export;

use Exception;
use InvalidArgumentException;
use Magento\Eav\Model\Entity\Attribute;
use Magento\Eav\Model\Entity\Attribute\Source\Store;
use Magento\Eav\Model\Entity\AttributeFactory;
use Magento\Framework\Data\Collection;
use Magento\ImportExport\Model\Export\Factory as CollectionFactory;
use Omnipro\ProductDetailExport\Model\Product\ProductTypeSource;
use Magento\Framework\App\ResourceConnection;

/**
 * AttributeCollectionProviderProductDetail class
 */
class AttributeCollectionProviderProductDetail
{
    public const ATTRIBUTE_SKU = 'sku';
    public const ATTRIBUTE_PRODUCT_TYPE = 'tipo_de_producto';
    public const ATTRIBUTE_CATEGORY_IDS = 'category_ids';
    public const ATTRIBUTE_EAN = 'ean';
    public const ATTRIBUTE_STORE_ID = 'store_id';

    /**
     * @var Collection
     */
    private $collection;

    /**
     * @var AttributeFactory
     */
    private $attributeFactory;

    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @param CollectionFactory $collectionFactory
     * @param AttributeFactory $attributeFactory
     * @param ResourceConnection $resource
     *
     * @throws InvalidArgumentException
     */
    public function __construct(
        CollectionFactory $collectionFactory,
        AttributeFactory $attributeFactory,
        ResourceConnection $resource
    ) {
        $this->collection = $collectionFactory->create(Collection::class);
        $this->attributeFactory = $attributeFactory;
        $this->resource = $resource;
    }

    /**
     * Gets the collection represented by product filters
     *
     * @throws Exception
     * @return Collection
     */
    public function get(): Collection
    {
        if (count($this->collection) === 0) {
            /** @var Attribute $skuAttribute */
            $skuAttribute = $this->attributeFactory->create();
            $skuAttribute->setId(self::ATTRIBUTE_SKU);
            $skuAttribute->setDefaultFrontendLabel('SKU');
            $skuAttribute->setAttributeCode(self::ATTRIBUTE_SKU);
            $skuAttribute->setBackendType('varchar');
            $this->collection->addItem($skuAttribute);

            /** @var Attribute $productTypeAttribute */
            $productTypeAttribute = $this->attributeFactory->create();
            $productTypeAttribute->setId(self::ATTRIBUTE_PRODUCT_TYPE);
            $productTypeAttribute->setDefaultFrontendLabel('Product Type');
            $productTypeAttribute->setAttributeCode(self::ATTRIBUTE_PRODUCT_TYPE);
            $productTypeAttribute->setBackendType('int');
            $productTypeAttribute->setFrontendInput('multiselect');
            $productTypeAttribute->setSourceModel(ProductTypeSource::class);
            $this->collection->addItem($productTypeAttribute);

            /** @var Attribute $categoryIdsAttribute */
            $categoryIdsAttribute = $this->attributeFactory->create();
            $categoryIdsAttribute->setId(self::ATTRIBUTE_CATEGORY_IDS);
            $categoryIdsAttribute->setDefaultFrontendLabel('Category IDs');
            $categoryIdsAttribute->setAttributeCode(self::ATTRIBUTE_CATEGORY_IDS);
            $categoryIdsAttribute->setBackendType('text');
            $categoryIdsAttribute->setFrontendInput('text');
            $this->collection->addItem($categoryIdsAttribute);

            /** @var Attribute $storeAttribute */
            $storeAttribute = $this->attributeFactory->create();
            $storeAttribute->setId(self::ATTRIBUTE_STORE_ID);
            $storeAttribute->setDefaultFrontendLabel(self::ATTRIBUTE_STORE_ID);
            $storeAttribute->setAttributeCode(self::ATTRIBUTE_STORE_ID);
            $storeAttribute->setBackendType('int');
            $storeAttribute->setFrontendInput('multiselect');
            $storeAttribute->setSourceModel(Store::class);
            $this->collection->addItem($storeAttribute);

            /** @var Attribute $eanAttribute */
            $eanAttribute = $this->attributeFactory->create();
            $eanAttribute->setId(self::ATTRIBUTE_EAN);
            $eanAttribute->setDefaultFrontendLabel('EAN');
            $eanAttribute->setAttributeCode(self::ATTRIBUTE_EAN);
            $eanAttribute->setBackendType('varchar');
            $this->collection->addItem($eanAttribute);
        }

        return $this->collection;
    }
}
