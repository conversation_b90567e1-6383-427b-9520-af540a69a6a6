<?php
/**
 * Copyright (c) 2025 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> <<EMAIL>> | Omnipro Team
 * @date 17/2/2025
 * @category Omnipro
 */

declare(strict_types=1);

namespace Omnipro\ProductDetailExport\Model\Export;

use Exception;
use Magento\Catalog\Model\Product;
use Magento\Framework\Data\Collection;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Exception\LocalizedException;
use Magento\ImportExport\Model\Export\AbstractEntity;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\File\Csv;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\Store;
use Magento\Store\Model\StoreManagerInterface;
use Magento\ImportExport\Model\Export\Factory;
use Magento\ImportExport\Model\ResourceModel\CollectionByPagesIteratorFactory;
use Omnipro\ProductDetailExport\Api\ProductDetailExportInterface;
use Omnipro\ProductDetailExport\Model\Export\AttributeCollectionProviderProductDetail;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Model\Product\Visibility;

/**
 * Handles product detail export functionality.
 */
class ProductDetailExport extends AbstractEntity implements ProductDetailExportInterface
{
    protected array $visibilityMap = [
        Visibility::VISIBILITY_NOT_VISIBLE => 'Not Visible Individually',
        Visibility::VISIBILITY_IN_CATALOG => 'Catalog',
        Visibility::VISIBILITY_IN_SEARCH => 'Search',
        Visibility::VISIBILITY_BOTH => 'Catalog & Search'
    ];

    /**
     * Retrieves the permanent attribute headers for export.
     *
     * @return string[]
     */
    protected $_permanentAttributes = [
        ProductDetailExportInterface::COLUMN_ID,
        ProductDetailExportInterface::COLUMN_SKU,
        ProductDetailExportInterface::COLUMN_MOTHER_SKU,
        ProductDetailExportInterface::COLUMN_PRODUCT_TYPE,
        ProductDetailExportInterface::COLUMN_EAN,
        ProductDetailExportInterface::COLUMN_NAME,
        ProductDetailExportInterface::COLUMN_SIZE,
        ProductDetailExportInterface::COLUMN_DEPARTAMENTO,
        ProductDetailExportInterface::COLUMN_COLLECCION,
        ProductDetailExportInterface::COLUMN_TIPO_DE_PRODUCTO,
        ProductDetailExportInterface::COLUMN_GENERO,
        ProductDetailExportInterface::COLUMN_MARCA,
        ProductDetailExportInterface::COLUMN_CATEGORY_ID,
        ProductDetailExportInterface::COLUMN_STORE_VIEW_CODE,
        ProductDetailExportInterface::COLUMN_PRODUCT_ONLINE,
        ProductDetailExportInterface::COLUMN_VISIBILITY,
        ProductDetailExportInterface::COLUMN_BASE_IMAGE,
        ProductDetailExportInterface::COLUMN_SMALL_IMAGE,
        ProductDetailExportInterface::COLUMN_THUMBNAIL_IMAGE,
        ProductDetailExportInterface::COLUMN_SWATCH_IMAGE,
        ProductDetailExportInterface::COLUMN_ADDITIONAL_IMAGES
    ];

    /**
     * ProductDetailExport constructor
     *
     * @param Csv $csvProcessor
     * @param DirectoryList $directoryList
     * @param CollectionFactory $productCollectionFactory
     * @param ResourceConnection $resource
     * @param ScopeConfigInterface $scopeConfig
     * @param StoreManagerInterface $storeManager
     * @param Factory $collectionFactory
     * @param CollectionByPagesIteratorFactory $resourceColFactory
     * @param \Omnipro\ProductDetailExport\Model\Export\AttributeCollectionProviderProductDetail $attributeCollectionProviderProductDetail
     * @param AttributeRepositoryInterface $attributeRepositoryInterface
     * @param CategoryRepositoryInterface $categoryRepository
     */
    public function __construct(
        protected readonly Csv $csvProcessor,
        protected readonly DirectoryList $directoryList,
        protected readonly CollectionFactory $productCollectionFactory,
        protected readonly ResourceConnection $resource,
        protected readonly ScopeConfigInterface $scopeConfig,
        protected readonly StoreManagerInterface $storeManager,
        protected readonly Factory $collectionFactory,
        protected readonly CollectionByPagesIteratorFactory $resourceColFactory,
        protected readonly AttributeCollectionProviderProductDetail $attributeCollectionProviderProductDetail,
        protected readonly AttributeRepositoryInterface $attributeRepositoryInterface,
        protected readonly CategoryRepositoryInterface $categoryRepository
    ) {
        parent::__construct($scopeConfig, $storeManager, $collectionFactory, $resourceColFactory);
    }

    /**
     * Exports product details into CSV format.
     *
     * @return string CSV content.
     * @throws FileSystemException|LocalizedException
     */
    public function export()
    {
        $writer = $this->getWriter();

        $writer->setHeaderCols($this->_getHeaderColumns());
        $productsData = $this->getFilteredProductData();

        foreach ($productsData as $product) {
            $writer->writeRow([
                self::COLUMN_ID => $product->getId(),
                self::COLUMN_SKU => $product->getSku(),
                self::COLUMN_MOTHER_SKU => $product->getParentSku(),
                self::COLUMN_NAME => $product->getName(),
                self::COLUMN_PRODUCT_TYPE => $product->getTypeId(),
                self::COLUMN_EAN => $product->getEan(),
                self::COLUMN_SIZE => $this->getAttributeText($product, 'size'),
                self::COLUMN_DEPARTAMENTO => $this->getAttributeText($product, 'departamento'),
                self::COLUMN_COLLECCION => $this->getAttributeText($product, 'coleccion'),
                self::COLUMN_TIPO_DE_PRODUCTO => $this->getAttributeText($product, 'tipo_de_producto'),
                self::COLUMN_GENERO => $this->getAttributeText($product, 'genero'),
                self::COLUMN_MARCA => $this->getAttributeText($product, 'marca'),
                self::COLUMN_CATEGORY_ID => implode(', ', (array) $product->getCategoryIds()),
                self::COLUMN_STORE_VIEW_CODE => $this->storeManager->getStore($product->getStoreId())->getCode(),
                self::COLUMN_PRODUCT_ONLINE => $product->getStatus(),
                self::COLUMN_VISIBILITY => implode(', ', array_map(
                    fn($id) => $this->visibilityMap[$id] ?? 'Unknown',
                    (array) $product->getVisibility()
                )),
                self::COLUMN_BASE_IMAGE => $product->getImage(),
                self::COLUMN_SMALL_IMAGE => $product->getSmallImage(),
                self::COLUMN_THUMBNAIL_IMAGE => $product->getThumbnail(),
                self::COLUMN_SWATCH_IMAGE => $product->getSwatchImage(),
                self::COLUMN_ADDITIONAL_IMAGES => $product->getMediaGallery()
            ]);
        }

        return $writer->getContents();
    }

    /**
     * Retrieves a filtered collection of product data for export.
     *
     * @return array Filtered product data.
     */
    private function getFilteredProductData(): array
    {
        $params = $this->_parameters;
        $storeIds = !empty($params['export_filter']['store_id'])
            ? (array) $params['export_filter']['store_id']
            : [Store::DEFAULT_STORE_ID];

        $allProducts = [];

        foreach ($storeIds as $storeId) {
            $productCollection = $this->productCollectionFactory->create()
                ->setStoreId($storeId)
                ->addAttributeToSelect([
                    'sku',
                    'name',
                    'type_id',
                    'ean',
                    'size',
                    'category_ids',
                    'departamento',
                    'coleccion',
                    'tipo_de_producto',
                    'genero',
                    'marca',
                    'status',
                    'visibility',
                    'image',
                    'small_image',
                    'thumbnail',
                    'swatch_image',
                    'media_gallery'
                ])
                ->setFlag('has_stock_status_filter', true)
                ->addStoreFilter($storeId);

            // Get parent SKU
            $productCollection->getSelect()->columns([
                'parent_sku' => new \Zend_Db_Expr(
                    "(SELECT parent.sku
                  FROM {$productCollection->getTable('catalog_product_entity')} parent
                  WHERE parent.row_id =
                        (SELECT configurable.parent_id
                         FROM {$productCollection->getTable('catalog_product_relation')} configurable
                         WHERE configurable.child_id = e.entity_id
                         LIMIT 1)
                )")
            ]);

            // Categories filter
            if (!empty($params['export_filter']['category_ids'])) {
                $categoryIds = (array) $params['export_filter']['category_ids'];
                $productCollection->addCategoriesFilter(['in' => $categoryIds]);
            }

            // EAN filter
            if (!empty($params['export_filter']['ean'])) {
                $eanFilter = (array) $params['export_filter']['ean'];
                $productCollection->addFieldToFilter('ean', ['in' => $eanFilter]);
            }

            // SKU filter
            if (!empty($params['export_filter']['sku'])) {
                $skuFilter = (array) $params['export_filter']['sku'];
                $productCollection->addFieldToFilter('sku', ['in' => $skuFilter]);
            }

            // product type filter
            if (!empty($params['export_filter']['tipo_de_producto'])) {
                $productCollection->addFieldToFilter('tipo_de_producto', $params['export_filter']['tipo_de_producto']);
            }

            // Populate array of products
            foreach ($productCollection->getItems() as $product) {
                $allProducts[] = $product;
            }
        }

        return $allProducts;
    }


    /**
     * @return string
     */
    public function getEntityTypeCode(): string
    {
        return 'omnipro_product_export';
    }

    /**
     * @param $item
     * @return void
     */
    public function exportItem($item)
    {
        // TODO: Implement exportItem() method.
    }

    /**
     * @return array|string[]
     */
    public function getPermanentAttributes(): array
    {
        return $this->_permanentAttributes;
    }

    /**
     * @return array|string[]
     */
    protected function _getHeaderColumns(): array
    {
        return $this->getPermanentAttributes();
    }

    /**
     * @return void
     */
    protected function _getEntityCollection()
    {
    }

    /**
     * @inheritdoc
     * @throws Exception
     */
    public function getAttributeCollection(): Collection
    {
        return $this->attributeCollectionProviderProductDetail->get();
    }

    /**
     * Get label of dropdown/multiselect  product attribute
     *
     * @param Product $product
     * @param string $attributeCode
     * @return string
     */
    private function getAttributeText(Product $product, string $attributeCode)
    {
        $optionText = '';

        if ($product && $attributeCode) {
            if ($product->hasData($attributeCode)) {
                $attribute = $product->getResource()->getAttribute($attributeCode);
                if ($attribute->usesSource()) {
                    $optionText = $attribute->getSource()->getOptionText($product->getData($attributeCode));
                }
            }
        }

        return $optionText;
    }
}
