<?php
/**
 * Copyright (c) 2025 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> <<EMAIL>> | Omnipro Team
 * @date 17/2/2025
 * @category Omnipro
 */

declare(strict_types=1);

namespace Omnipro\ProductDetailExport\Model\Product;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\AbstractAttribute;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\Option\CollectionFactory as OptionCollectionFactory;
use Magento\Eav\Model\Entity\AttributeFactory;
use Magento\Framework\Exception\LocalizedException;

/**
 * Custom product type source class for tipo_de_producto attribute
 */
class ProductTypeSource
{
    /**
     * @var AbstractAttribute
     */
    protected AbstractAttribute $attribute;

    /**
     * @param OptionCollectionFactory $optionCollectionFactory
     */
    public function __construct(
        protected OptionCollectionFactory $optionCollectionFactory,
        protected AttributeFactory $attributeFactory
    ) {
    }

    /**
     * Retrieves all available options for the custom attribute "tipo_de_producto".
     *
     * @return array List of available options.
     * @throws LocalizedException
     */
    public function getAllOptions(): array
    {
        $options = [];
        $attributeId = $this->getAttributeId('tipo_de_producto');

        if (!$attributeId) {
            return [];
        }

        $collection = $this->optionCollectionFactory->create()
            ->setAttributeFilter($attributeId)
            ->setPositionOrder('asc', true)
            ->load();


        foreach ($collection as $option) {
            $options[] = [
                'value' => $option->getId(),
                'label' => $option->getValue()
            ];
        }

        return $options;
    }

    /**
     * Retrieves the attribute associated with the source.
     *
     * @return AbstractAttribute|null
     */
    public function getAttribute(): AbstractAttribute
    {
        return $this->attribute;
    }

    /**
     * Sets the attribute for the source.
     *
     * @param AbstractAttribute $attribute The attribute to set.
     * @return self
     */
    public function setAttribute(AbstractAttribute $attribute): self
    {
        $this->attribute = $attribute;
        return $this;
    }

    /**
     * Retrieves the attribute ID by its code.
     *
     * @return int|null The attribute ID or null if not found.
     * @throws LocalizedException
     */
    private function getAttributeId(string $attributeCode)
    {
        $attribute = $this->attributeFactory->create()->loadByCode(Product::ENTITY, $attributeCode);
        return $attribute->getId();
    }
}
