# Omnipro_PriceRange module

### Authors
- <PERSON> <<EMAIL>> || Omnipro Team

### Package:
Omnipro_PriceRange

## Associated tickets:
- https://omnipro.atlassian.net/browse/DEPPEDC20-281

## Omnipro_PriceRange module by Omni.pro

    omnipro/Omnipro_PriceRange

- [Main Functionalities](#markdown-header-main-functionalities)
- [Installation](#markdown-header-installation)

## Main Functionalities
- Display price range for configurable products.

## Installation

1. Unzip the module into `app/code/Omnipro/PriceRange`.
2. Enable the module:
   php bin/magento module:enable Omnipro_PriceRange
3. Apply database updates:
   php bin/magento setup:upgrade
4. Flush the cache:
   php bin/magento cache:flush
