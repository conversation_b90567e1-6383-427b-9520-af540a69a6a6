### Authors
- <PERSON> <<EMAIL>> || Omnipro Team

### Copyright:
Copyright (c) 2025 OmniPro (https://omni.pro/)

### Package:
Omnipro_PriceRange

# Changelog
All notable changes to this module will be documented in this file.

The format is based on Keep a Changelog,
and this module adheres to Semantic Versioning.

## [1.0.0] - 2025-02-17
### Added
- Creation of the `Omnipro_PriceRange` module.
- Implementation of the `AbstractProductPricePlugin` base class to avoid code duplication.
- `ProductListPlugin` to modify product prices in the product list.
- `ProductListWidgetPlugin` to modify product prices in the product widget.
- Functionality to display price range on the Product Detail Page (PDP).
    - Added `Configurable` block to handle price range logic.
    - Included JavaScript to toggle visibility of price range and original price based on configurable options.
