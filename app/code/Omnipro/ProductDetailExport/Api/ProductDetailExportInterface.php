<?php
/**
 * Copyright (c) 2025 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> <<EMAIL>> | Omnipro Team
 * @date 17/2/2025
 * @category Omnipro
 */

declare(strict_types=1);

namespace Omnipro\ProductDetailExport\Api;

/**
 * Interface ProductDetailExportInterface
 *
 * Defines the contract for exporting product details.
 */
interface ProductDetailExportInterface
{
    /**
     * Report column names.
     */
    public const COLUMN_ID = 'ID';
    public const COLUMN_SKU = 'SKU';
    public const COLUMN_MOTHER_SKU = 'MOTHER SKU';
    public const COLUMN_PRODUCT_TYPE = 'PRODUCT TYPE';
    public const COLUMN_EAN = 'EAN';
    public const COLUMN_NAME = 'NAME';
    const COLUMN_SIZE = 'SIZE';
    const COLUMN_DEPARTAMENTO = 'DEPARTAMENTO';
    const COLUMN_COLLECCION = 'COLECCION';
    const COLUMN_TIPO_DE_PRODUCTO = 'TIPO DE PRODUCTO';
    const COLUMN_GENERO = 'GENERO';
    const COLUMN_MARCA = 'MARCA';
    const COLUMN_CATEGORY_ID = 'CATEGORY ID';
    const COLUMN_STORE_VIEW_CODE = 'STORE VIEW CODE';
    const COLUMN_PRODUCT_ONLINE = 'PRODUCT ONLINE';
    const COLUMN_VISIBILITY = 'VISIBILITY';
    const COLUMN_BASE_IMAGE = 'BASE IMAGE';
    const COLUMN_SMALL_IMAGE = 'SMALL IMAGE';
    const COLUMN_THUMBNAIL_IMAGE = 'THUMBNAIL IMAGE';
    const COLUMN_SWATCH_IMAGE = 'SWATCH IMAGE';
    const COLUMN_ADDITIONAL_IMAGES = 'ADDITIONAL IMAGES';


    /**
     * Retrieves the permanent attribute headers for the export.
     *
     * @return string[] List of permanent attribute headers.
     */
    public function getPermanentAttributes(): array;
}
