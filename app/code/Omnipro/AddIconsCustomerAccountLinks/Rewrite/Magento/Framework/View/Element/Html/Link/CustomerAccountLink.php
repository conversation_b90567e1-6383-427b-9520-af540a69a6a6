<?php
declare(strict_types=1);

namespace Omnipro\AddIconsCustomerAccountLinks\Rewrite\Magento\Framework\View\Element\Html\Link;

use Magento\Customer\Block\Account\SortLink as SortLink;

/**
 * Class CustomerAccountLink
 * @package Omnipro\AddIconsCustomerAccountLinks\Rewrite\Magento\Framework\View\Element\Html\Link
 */
class CustomerAccountLink extends SortLink
{
    /**
     * Render block HTML
     *
     * @return string
     */
    protected function _toHtml()
    {
        if (false != $this->getTemplate()) {
            return parent::_toHtml();
        }

        $highlight = '';
        $iconClass = 'icon_account-link';
        $label = $this->getLabel();
        $labelWithoutAccents = strtr($label, [
            'á' => 'a',
            'é' => 'e',
            'í' => 'i',
            'ó' => 'o',
            'ú' => 'u',
            'ñ' => 'n',
            'Á' => 'A',
            'É' => 'E',
            'Í' => 'I',
            'Ó' => 'O',
            'Ú' => 'U',
            'Ñ' => 'N',
        ]);
        $iconClassLabel = $label ? str_replace(' ', '-', strtolower($labelWithoutAccents)) : '';

        if ($this->getIsHighlighted()) {
            $highlight = ' current';
        }

        if ($this->isCurrent()) {
            $html = '<li class="nav item current '. $iconClass .'  '. $iconClassLabel . '">';
            $html .= '<strong>'
                . $this->escapeHtml(__($this->getLabel()))
                . '</strong>';
            $html .= '</li>';
        } else {
            $html = '<li class="nav item '. $iconClass .' ' . $iconClassLabel . ' ' . $highlight . '"><a href="' . $this->escapeHtml($this->getHref()) . '"';
            $html .= $this->getTitle()
                ? ' title="' . $this->escapeHtml(__($this->getTitle())) . '"'
                : '';
            $html .= $this->getAttributesHtml() . '>';

            if ($this->getIsHighlighted()) {
                $html .= '<strong>';
            }

            $html .= $this->escapeHtml(__($this->getLabel()));

            if ($this->getIsHighlighted()) {
                $html .= '</strong>';
            }

            $html .= '</a></li>';
        }

        return $html;
    }
}

