# Omnipro_AddIconsCustomerAccountLinks module

### Authors
- <PERSON> <<EMAIL>> || Omnipro Team

### Package:
Omnipro_AddIconsCustomerAccountLinks

## Associated tickets:
- https://omnipro.atlassian.net/browse/S99DC-349


## Omnipro_AddIconsCustomerAccountLinks  module by Omni.pro

    omnipro/module-addiconscustomeraccountlinks

- [Main Functionalities](#markdown-header-main-functionalities)
- [Installation](#markdown-header-installation)

## Main Functionalities
- Add custom icons on Customer Account Nav
- Keep the icon when change class to current on li nav item

## Installation

- Unzip the zip file in `app/code/Omnipro`
- Enable the module by running `php bin/magento mod
- Apply database updates by running `php bin/magento
- Flush the cache by running `php bin/magento cache
