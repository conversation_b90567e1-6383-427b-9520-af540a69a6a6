<?php
/*
 * Created on Wed Nov 08 2023
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\ShippingByStock\Model
 * @category Omnipro
 * @copyright Copyright (c) 2023 Omnipro (https://www.omni.pro/)
 */

namespace Omnipro\ShippingByStock\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Omnipro\ShippingByStock\Api\ConfigInterface;

/**
 * Config Model definition
 */
class Config implements ConfigInterface
{
    /**
     * Paths to configuration
     *
     * @var string
     */
    private const XML_PATH_SHIPPING_STOCK_ENABLE = 'shipping/omnipro_shipping_by_stock/enable_shipping_by_stock';
    private const XML_PATH_WAREHOUSE = 'shipping/omnipro_shipping_by_stock/warehouses_home_delivery';
    private const XML_PATH_SHIPPING = 'shipping/omnipro_shipping_by_stock/shipping_methods_home_delivery';
    private const XML_PATH_ENABLE_VALIDATE_STOCK  = 'shipping/omnipro_shipping_by_stock/validate_stock';

    /**
     * Constructor
     *
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        private ScopeConfigInterface $scopeConfig
    ) {}

    /**
     * @inheritDoc
     */
    public function isEnableShippingByStock(int $storeId): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_SHIPPING_STOCK_ENABLE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @inheritDoc
     */
    public function getWarehousesHomeDelivery(int $storeId): null | string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_WAREHOUSE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @inheritDoc
     */
    public function getShippingMethodsHomeDelivery(int $storeId): null | string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_SHIPPING,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @inheritDoc
     */
    public function isEnableValidateShipping(int $storeId): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_ENABLE_VALIDATE_STOCK,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
