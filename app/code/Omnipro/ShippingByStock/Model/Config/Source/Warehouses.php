<?php
/*
 * Created on Wed Nov 22 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\ShippingByStock
 * @category Omnipro
 * @copyright Copyright (c) 2023 Omnipro (https://www.omni.pro/)
 */

declare(strict_types=1);

namespace Omnipro\ShippingByStock\Model\Config\Source;

use Magento\InventoryApi\Api\SourceRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;

/**
 * Class Warehouses
 */
class Warehouses
{
    /**
     * Construct
     *
     * @param SourceRepositoryInterface $sourceRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     */
    public function __construct(
        protected SourceRepositoryInterface $sourceRepository,
        protected SearchCriteriaBuilder $searchCriteriaBuilder
    ) {}

    /**
     * Get the list of sources excluding those marked as "Use as Pickup Location"
     *
     * @return array
     */
    public function toOptionArray(): array
    {
        $options = [];

        $searchCriteria = $this->searchCriteriaBuilder->create();
        $sources = $this->sourceRepository->getList($searchCriteria)->getItems();

        foreach ($sources as $source) {
            $options[] = [
                'value' => $source->getSourceCode(),
                'label' => $source->getName()
            ];
        }

        return $options;
    }
}
