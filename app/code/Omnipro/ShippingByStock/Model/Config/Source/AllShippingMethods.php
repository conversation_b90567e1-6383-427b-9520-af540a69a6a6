<?php
/**
 * Created on Mon Nov 18 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\ShippingByStock\Model\Config\Source
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
 */
declare(strict_types=1);

namespace Omnipro\ShippingByStock\Model\Config\Source;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Shipping\Model\Config;
use Magento\Store\Model\ScopeInterface;

/**
 * All Shipping Methods Source Model
 */
class AllShippingMethods implements OptionSourceInterface
{
    /**
     * Constructor
     *
     * @param ScopeConfigInterface $scopeConfig
     * @param Config $shippingConfig
     */
    public function __construct(
        private ScopeConfigInterface $scopeConfig,
        private Config $shippingConfig
    ) {
    }

    /**
     * @inheritDoc
     */
    public function toOptionArray() {
        $isActiveOnlyFlag = false;
        $methods = [['value' => '', 'label' => '']];
        $carriers = $this->shippingConfig->getAllCarriers();
        foreach ($carriers as $carrierCode => $carrierModel) {
            if (!$carrierModel->isActive() && (bool)$isActiveOnlyFlag === true) {
                continue;
            }
            $carrierMethods = $carrierModel->getAllowedMethods();
            if (!$carrierMethods) {
                continue;
            }
            $carrierTitle = $this->scopeConfig->getValue(
                'carriers/' . $carrierCode . '/title',
                ScopeInterface::SCOPE_STORE
            );
            $methods[$carrierCode] = ['label' => $carrierTitle, 'value' => []];
            foreach ($carrierMethods as $methodCode => $methodTitle) {

                /** Check it $carrierMethods array was well formed */
                if (!$methodCode) {
                     continue;
                }
                $methods[$carrierCode]['value'][] = [
                    'value' => $carrierCode . '_' . $methodCode,
                    'label' => '[' . $carrierCode . '] ' . $methodTitle,
                ];
            }
        }

        return $methods;
    }
}
