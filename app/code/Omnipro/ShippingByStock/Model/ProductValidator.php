<?php
/*
 * Created on Wed Nov 22 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\ShippingByStock
 * @category Omnipro
 * @copyright Copyright (c) 2023 Omnipro (https://www.omni.pro/)
 */

namespace Omnipro\ShippingByStock\Model;

use Infinite\ExtendInventoryReservations\Service\ReservationService;
use Magento\Framework\Exception\LocalizedException;
use Omnipro\ShippingByStock\Api\ProductValidatorInterface;
use Magento\InventoryApi\Api\SourceRepositoryInterface;
use Magento\InventoryApi\Api\GetSourceItemsBySkuInterface;
use Magento\InventoryApi\Api\SourceItemRepositoryInterface;
use Magento\InventoryApi\Api\StockRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Inventory\Model\ResourceModel\SourceItem\CollectionFactory;
use Magento\Store\Model\StoreManagerInterface;
use Omnipro\StockAvailability\Api\ConfigInterface;
use Omnipro\ShippingByStock\Api\ConfigInterface as ShippingByStockConfig;

/**
 * Product validator class
 */
class ProductValidator implements ProductValidatorInterface
{
    /**
     * PUBLIC CONST
     */
    public const STATUS_WAREHOUSE = '1';

    /**
     * Constructor.
     *
     * @param SourceRepositoryInterface $sourceRepository
     * @param GetSourceItemsBySkuInterface $getSourceItemsBySku
     * @param SourceItemRepositoryInterface $sourceItemRepository
     * @param StockRepositoryInterface $stockRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param CollectionFactory $sourceItemCollectionFactory
     * @param StoreManagerInterface $storeManager
     * @param ReservationService $reservationService
     * @param ConfigInterface $config
     * @param ShippingByStockConfig $shippingByStockConfig
     */
    public function __construct(
        private SourceRepositoryInterface $sourceRepository,
        private GetSourceItemsBySkuInterface $getSourceItemsBySku,
        private SourceItemRepositoryInterface $sourceItemRepository,
        private StockRepositoryInterface $stockRepository,
        private SearchCriteriaBuilder $searchCriteriaBuilder,
        private CollectionFactory $sourceItemCollectionFactory,
        private StoreManagerInterface $storeManager,
        private ReservationService $reservationService,
        private ConfigInterface $config,
        private ShippingByStockConfig $shippingByStockConfig,
    ) {
    }

    /**
     * @inheritDoc
     * @throws LocalizedException
     */
    public function validateProductBeforeUpdateCart(
        array $infoQuote,
    ): bool {
        $storeId = (int)$this->storeManager->getStore()->getId();
        if ($this->config->isEnableTotalStock($storeId)) {
            return true;
        }
        $sourceItems = $this->getSourceItemsBySkus(array_keys($infoQuote));

        $qtyDelivery = $maxStorePickUpQty = 0;
        $availableSourcesCommon = [];

        foreach ($infoQuote as $sku => &$detailInfoQuote) {
            $qtyDelivery = $maxStorePickUpQty = 0;
            $sources = [];

            foreach ($sourceItems as $sourceItem) {
                if ($sourceItem->getSku() !== $sku) {
                    continue;
                }

                $qty = (float)$sourceItem->getQuantity();
                $sourceCode = $sourceItem->getSourceCode();
                if (!(bool)$sourceItem->getIsPickupLocationActive()) {
                    $qtyDelivery += $qty;
                } else {
                    $maxStorePickUpQty = max($maxStorePickUpQty, $qty);
                    $reservedQtyStorePickUp = $this->reservationService->getReservationsBySkuAndSource(
                        $sku,
                        $this->shippingByStockConfig->isEnableValidateShipping($storeId),
                        ReservationService::SHIPPING_TYPE_STORE_PICKUP,
                        $sourceCode
                    );
                    if ($qty + $reservedQtyStorePickUp >= $detailInfoQuote['qty']) {
                        $sources[] = $sourceCode;
                    }
                }
            }
            $reservedQtyDelivery = $this->reservationService->getReservationsBySku(
                $sku,
                $this->shippingByStockConfig->isEnableValidateShipping($storeId),
                ReservationService::SHIPPING_TYPE_HOME_DELIVERY
            );

            $qtyDelivery += $reservedQtyDelivery;

            $availableSourcesCommon[] = $sources;

            $detailInfoQuote['qtyDelivery'] = $qtyDelivery;
            $detailInfoQuote['qtyStorePickUp'] = $maxStorePickUpQty;
            $detailInfoQuote['canUseDelivery'] = $detailInfoQuote['qty'] <= $qtyDelivery;
            $detailInfoQuote['canUseStorePickUp'] = $detailInfoQuote['qty'] <= $maxStorePickUpQty;
        }

        $statusHomeDelivery = $statusStorePickUp = true;
        if (!empty($availableSourcesCommon) && count($availableSourcesCommon) > 1) {
            $availableSourcesCommon = call_user_func_array('array_intersect', $availableSourcesCommon);

            if (empty($availableSourcesCommon)) {
                $statusStorePickUp = false;
            }
        }

        foreach ($infoQuote as $subInfoQuote) {
            $statusHomeDelivery = $statusHomeDelivery && $subInfoQuote['canUseDelivery'];
            $statusStorePickUp = $statusStorePickUp && $subInfoQuote['canUseStorePickUp'];
        }

        return $statusHomeDelivery || $statusStorePickUp;
    }

    /**
     * @inheritDoc
     * @throws LocalizedException
     */
    public function checkProductStockInHomeDeliveryWarehouses($product, array $warehousesHomeDelivery, $qty): bool
    {
        $quantityWarehouse = 0;
        $websiteCode = $this->storeManager->getWebsite()->getCode();
        $storeId = $this->storeManager->getStore()->getId();
        $sourceItems = $this->getSourceItemsBySku($product->getSku(), $websiteCode);

        foreach ($warehousesHomeDelivery as $warehouseCode) {
            try {
                foreach ($sourceItems as $sourceItem) {
                    if ($sourceItem->getSourceCode() === $warehouseCode &&
                        $sourceItem->getStatus() == self::STATUS_WAREHOUSE) {
                        $quantityWarehouse += $sourceItem->getQuantity();
                    }
                }
            } catch (NoSuchEntityException $e) {
                continue;
            }
        }

        $reservationQtyDelivery = $this->reservationService->getReservationsBySku(
            $product->getSku(),
            $this->shippingByStockConfig->isEnableValidateShipping($storeId),
            ReservationService::SHIPPING_TYPE_HOME_DELIVERY,

        );
        $quantityWarehouse += $reservationQtyDelivery;

        return $quantityWarehouse >= $qty;
    }

    /**
     * Get sources by SKU and website code
     *
     * @param string $sku
     * @param string $websiteCode
     * @return array
     */
    private function getSourceItemsBySku(string $sku, string $websiteCode): array
    {
        $sourceItemCollection = $this->sourceItemCollectionFactory->create();
        $sourceItemCollection->addFieldToFilter('sku', $sku)
            ->getSelect()
            ->join(
                ['inventory_source_stock_link' => $sourceItemCollection->getTable('inventory_source_stock_link')],
                'main_table.source_code = inventory_source_stock_link.source_code',
                []
            )
            ->join(
                ['inventory_stock_sales_channel' => $sourceItemCollection->getTable('inventory_stock_sales_channel')],
                'inventory_source_stock_link.stock_id = inventory_stock_sales_channel.stock_id',
                []
            )
            ->where('inventory_stock_sales_channel.type = ?', 'website')
            ->where('inventory_stock_sales_channel.code = ?', $websiteCode);
        return $sourceItemCollection->getItems();
    }

    /**
     * Get source items by skus
     *
     * @param array $skus
     * @return array
     * @throws LocalizedException
     */
    private function getSourceItemsBySkus(array $skus): array
    {
        $sourceItemCollection = $this->sourceItemCollectionFactory->create();
        $websiteCode = $this->storeManager->getWebsite()->getCode();

        $sourceItemCollection->addFieldToFilter('sku', ['in' => $skus])
            ->addFieldToFilter('status', 1)
            ->addFieldToFilter('inventory_source.enabled', 1)
            ->getSelect()
            ->join(
                ['inventory_source' => $sourceItemCollection->getTable('inventory_source')],
                'main_table.source_code = inventory_source.source_code',
                ['name', 'is_pickup_location_active']
            )
            ->join(
                ['inventory_source_stock_link' => $sourceItemCollection->getTable('inventory_source_stock_link')],
                'main_table.source_code = inventory_source_stock_link.source_code',
                []
            )
            ->join(
                ['inventory_stock_sales_channel' => $sourceItemCollection->getTable('inventory_stock_sales_channel')],
                'inventory_source_stock_link.stock_id = inventory_stock_sales_channel.stock_id',
                []
            )
            ->where('inventory_stock_sales_channel.type = ?', 'website')
            ->where('inventory_stock_sales_channel.code = ?', $websiteCode);

        return $sourceItemCollection->getItems();
    }
}
