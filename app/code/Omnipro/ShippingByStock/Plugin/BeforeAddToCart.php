<?php
/*
 * Created on Wed Nov 22 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\ShippingByStock
 * @category Omnipro
 * @copyright Copyright (c) 2023 Omnipro (https://www.omni.pro/)
 */

namespace Omnipro\ShippingByStock\Plugin;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Product;
use Magento\Quote\Model\Quote;
use Magento\Framework\Exception\LocalizedException;
use Magento\InventoryApi\Api\GetSourceItemsBySkuInterface;
use Magento\InventoryApi\Api\SourceRepositoryInterface;
use Magento\Quote\Api\Data\CartInterface;
use Omnipro\ShippingByStock\Api\ConfigInterface;
use Omnipro\ShippingByStock\Api\ProductValidatorInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;

/**
 * Class before add to cart plugin
 */
class BeforeAddToCart
{
    /**
     * Constructor
     *
     * @param GetSourceItemsBySkuInterface $getSourceItemsBySku
     * @param SourceRepositoryInterface $sourceRepository
     * @param ConfigInterface $configInterface
     * @param ProductValidatorInterface $productValidator
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        private GetSourceItemsBySkuInterface $getSourceItemsBySku,
        private SourceRepositoryInterface $sourceRepository,
        private ConfigInterface $configInterface,
        private ProductValidatorInterface $productValidator,
        private StoreManagerInterface $storeManager
    ) {}

    /**
     * Before plugin for addProduct method in Quote model
     *
     * @param Quote $subject
     * @param Product $product
     * @param array|null $request
     * @param null|float $processMode
     * @return array
     * @throws LocalizedException
     */
    public function beforeAddProduct(Quote $subject, $product, $request = null, $processMode = null)
    {
        $storeId = (int)$this->storeManager->getStore()->getId();
        if (!$this->configInterface->isEnableShippingByStock($storeId)) {
            return [$product, $request, $processMode];
        }

        if ($product->getTypeId() === Configurable::TYPE_CODE && isset($request['super_attribute'])) {
            $childProduct = $this->getSelectedChildProduct($product, $request);
            if (!$childProduct) {
                throw new LocalizedException(__('The requested quantity is not available.'));
            }

            $cartInfo = $this->getInfoQuote($childProduct, $subject, $request['qty']);
            if (!$this->productValidator->validateProductBeforeUpdateCart($cartInfo)) {
                throw new LocalizedException(__('The requested quantity is not available.'));
            }
        } else {
            $request = !isset($request['qty'])? $request: $request['qty'];
            $cartInfo = $this->getInfoQuote($product, $subject, $request);
            if (!$this->productValidator->validateProductBeforeUpdateCart($cartInfo)) {
                throw new LocalizedException(__('The requested quantity is not available.'));
            }
        }
    }

    /**
     * Get selected child product
     *
     * @param Product $parentProduct
     * @param array $request
     * @return ProductInterface|null
     */
    private function getSelectedChildProduct(Product $parentProduct, $request): ?ProductInterface
    {
        $typeInstance = $parentProduct->getTypeInstance();

        return $typeInstance->getProductByAttributes($request['super_attribute'], $parentProduct);
    }

    /**
     * Get info from quote
     *
     * @param ProductInterface $product
     * @param CartInterface $quote
     * @param float $requestedQty
     * @return array
     */
    private function getInfoQuote(ProductInterface $product, CartInterface $quote, float $requestedQty)
    {
        $info[$product->getSku()] = ['qty' => $requestedQty];

        /**
         * @var Quote $quote
         */
        foreach ($quote->getAllVisibleItems() as $item) {
            if (empty($item->getChildren())) {
                continue;
            }

            $info[$item->getSku()] = ['qty' => $item->getSku() === $product->getSku()
                ? $item->getQty() + $requestedQty : $item->getQty()];
        }

        return $info;
    }
}
