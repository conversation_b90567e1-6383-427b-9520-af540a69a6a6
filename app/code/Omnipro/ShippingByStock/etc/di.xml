<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Quote\Model\Quote">
        <plugin name="omnipro_before_add_product_plugin" type="Omnipro\ShippingByStock\Plugin\BeforeAddToCart"/>
    </type>

    <preference for="Omnipro\ShippingByStock\Api\ConfigInterface" type="Omnipro\ShippingByStock\Model\Config" />
    <preference for="Omnipro\ShippingByStock\Api\ProductValidatorInterface" type="Omnipro\ShippingByStock\Model\ProductValidator"/>

    <type name="Magento\Checkout\Controller\Sidebar\UpdateItemQty">
        <plugin name="sidebar_update_item_qty_plugin" type="Omnipro\ShippingByStock\Plugin\SidebarUpdateItemQtyPlugin" />
    </type>

    <type name="Magento\Checkout\Controller\Cart\UpdateItemQty">
        <plugin name="cart_update_item_qty_plugin" type="Omnipro\ShippingByStock\Plugin\CartUpdateItemQtyPlugin" />
    </type>

    <type name="Magento\Shipping\Model\Shipping">
        <plugin name="omnipro_shipping_methods_shipping_plugin"
                type="Omnipro\ShippingByStock\Plugin\ShippingRatesPlugin" />
    </type>

</config>
