<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="shipping">
            <group id="omnipro_shipping_by_stock" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Shipping By Stock Settings</label>
                <field id="enable_shipping_by_stock" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enabled functionality shipping by stock</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="shipping_methods_home_delivery" translate="label" type="multiselect" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allowed Shipping Methods for Home Delivery</label>
                    <source_model>Omnipro\ShippingByStock\Model\Config\Source\AllShippingMethods</source_model>
                    <depends>
                        <field id="enable_shipping_by_stock">1</field>
                    </depends>
                </field>
                <field id="warehouses_home_delivery" translate="label" type="multiselect" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Warehouses for Home Delivery Shipping Method</label>
                    <source_model>Omnipro\ShippingByStock\Model\Config\Source\Warehouses</source_model>
                    <depends>
                        <field id="enable_shipping_by_stock">1</field>
                    </depends>
                </field>
                <field id="validate_stock" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enable Validate by shipping type</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
