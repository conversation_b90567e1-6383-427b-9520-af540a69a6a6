define([
    'Magento_Ui/js/modal/alert',
    'jquery'
], function (alert, $) {
    'use strict';

    let updateShoppingCartWidgetMixin = {
        /**
         * Form validation failed.
         */
        onError: function (response) {
            var that = this,
                elm,
                responseData = [];

            try {
                responseData = JSON.parse(response['error_message']);
            } catch (error) {
            }

            if (response['error_message']) {
                try {
                    $.each(responseData, function (index, data) {

                        if (data.itemId !== undefined) {
                            elm = $('#cart-' + data.itemId + '-qty');
                            elm.val(elm.attr('data-item-qty'));
                        }
                        response['error_message'] = data.error;
                    });
                } catch (e) {}
                alert({
                    content: response['error_message'],
                    actions: {
                        /** @inheritdoc */
                        always: function () {
                            window.location.reload();
                        }
                    }
                });
            } else {
                this.submitForm();
            }
        },
    };

    return function (targetWidget) {
        $.widget('mage.updateShoppingCart', targetWidget, updateShoppingCartWidgetMixin);

        return $.mage.updateShoppingCart;
    };
});
