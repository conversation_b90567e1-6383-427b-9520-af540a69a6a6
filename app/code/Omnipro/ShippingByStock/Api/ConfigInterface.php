<?php

/*
 * Created on Wed Nov 08 2023
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\ShippingByStock\Api
 * @category Omnipro
 * @copyright Copyright (c) 2023 Omnipro (https://www.omni.pro/)
 */

namespace Omnipro\ShippingByStock\Api;

/**
 * Interface ConfigInterface defines the methods to get the configuration
 */
interface ConfigInterface
{

    /**
     * Is enable
     *
     *
     * @param int $storeId
     * @return bool
     */
    public function isEnableShippingByStock(int $storeId): bool;

    /**
     * Is free shipping functionality
     *
     * @param int $storeId
     * @return string|null
     */
    public function getWarehousesHomeDelivery(int $storeId): null | string;

    /**
     * Get delivery methods
     *
     * @param int $storeId
     * @return string|null
     */
    public function getShippingMethodsHomeDelivery(int $storeId): null | string;

    /**
     * Is enable
     *
     * @param int $storeId
     * @return bool
     */
    public function isEnableValidateShipping(int $storeId): bool;
}
