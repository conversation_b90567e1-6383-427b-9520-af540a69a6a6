<?php
/*
 * Created on Wed Nov 22 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\ShippingByStock
 * @category Omnipro
 * @copyright Copyright (c) 2023 Omnipro (https://www.omni.pro/)
 */

namespace Omnipro\ShippingByStock\Api;

use Magento\Catalog\Model\Product;
use Magento\Catalog\Api\Data\ProductInterface;

/**
 * Interface
 */
interface ProductValidatorInterface
{
    /**
     * Validate product before update cart
     *
     * @param array $infoQuote
     * @return bool
     */
    public function validateProductBeforeUpdateCart(array $infoQuote): bool;

    /**
     * Check if product has stock in any of the home delivery warehouses.
     *
     * @param Product | ProductInterface $product
     * @param array $warehousesHomeDelivery
     * @param float $qty
     * @return bool
     */
    public function checkProductStockInHomeDeliveryWarehouses(Product | ProductInterface $product, array $warehousesHomeDelivery, float $qty): bool;
}
