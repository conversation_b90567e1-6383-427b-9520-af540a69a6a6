<?xml version="1.0"?>
<!--
/**
 * Copyright © 2024 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> <<EMAIL>>
 * @date        10/02/25, 03:00 PM
 * @category    Omnipro
 * @module      Omnipro/NewsletterCustomization
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="adminhtml.newslettrer.subscriber.grid.columnSet">
            <block class="Magento\Backend\Block\Widget\Grid\Column" name="adminhtml.newslettrer.subscriber.grid.columnSet.first_name" as="first_name">
                <arguments>
                    <argument name="header" xsi:type="string" translate="true">First Name</argument>
                    <argument name="index" xsi:type="string">first_name</argument>
                    <argument name="header_css_class" xsi:type="string">col-id</argument>
                    <argument name="column_css_class" xsi:type="string">col-id</argument>
                </arguments>
            </block>
            <block class="Magento\Backend\Block\Widget\Grid\Column" name="adminhtml.newslettrer.subscriber.grid.columnSet.last_name" as="last_name">
                <arguments>
                    <argument name="header" xsi:type="string" translate="true">Last Name</argument>
                    <argument name="index" xsi:type="string">last_name</argument>
                    <argument name="header_css_class" xsi:type="string">col-id</argument>
                    <argument name="column_css_class" xsi:type="string">col-id</argument>
                </arguments>
            </block>
            <block class="Magento\Backend\Block\Widget\Grid\Column" name="adminhtml.newslettrer.subscriber.grid.columnSet.gender" as="gender">
                <arguments>
                    <argument name="header" xsi:type="string" translate="true">Gender</argument>
                    <argument name="index" xsi:type="string">gender</argument>
                    <argument name="header_css_class" xsi:type="string">col-id</argument>
                    <argument name="column_css_class" xsi:type="string">col-id</argument>
                </arguments>
            </block>
            <block class="Magento\Backend\Block\Widget\Grid\Column" name="adminhtml.newslettrer.subscriber.grid.columnSet.dob" as="dob">
                <arguments>
                    <argument name="header" xsi:type="string" translate="true">Date of Birthday</argument>
                    <argument name="index" xsi:type="string">dob</argument>
                    <argument name="header_css_class" xsi:type="string">col-id</argument>
                    <argument name="column_css_class" xsi:type="string">col-id</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>