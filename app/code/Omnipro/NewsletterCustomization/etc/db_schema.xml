<?xml version="1.0"?>
<!--
/**
 * Copyright © 2024 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> <<EMAIL>>
 * @date        10/02/25, 03:00 PM
 * @category    Omnipro
 * @module      Omnipro/NewsletterCustomization
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="newsletter_subscriber" resource="default" engine="innodb" comment="Newsletter Subscriber">
        <column xsi:type="varchar" name="first_name" comment="Subscriber Name"/>
        <column xsi:type="varchar" name="last_name" comment="Subscriber Lastname"/>
        <column xsi:type="varchar" name="gender" comment="Subscriber Gender"/>
        <column xsi:type="datetime" name="dob" comment="Subscriber Date of birthday"/>
    </table>
</schema>
