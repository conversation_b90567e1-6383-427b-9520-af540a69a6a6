<?php

/**
 * Copyright © 2024 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> <<EMAIL>>
 * @date        10/02/25, 03:00 PM
 * @category    Omnipro
 * @module      Omnipro/NewsletterCustomization
 */
declare(strict_types=1);

namespace Omnipro\NewsletterCustomization\Plugin\Newsletter;

use Magento\Framework\App\Request\Http;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Newsletter\Model\Subscriber;
use Magento\Newsletter\Model\SubscriberFactory;
use Magento\Newsletter\Model\SubscriptionManager;
use Magento\Newsletter\Model\ResourceModel\Subscriber as SubscriberResource;
use Magento\Store\Model\StoreManagerInterface;

class SubscriptionManagerPlugin
{
    /**
     * Constructor
     *
     * @param Http $request
     * @param SubscriberFactory $subscriberFactory
     * @param StoreManagerInterface $storeManager
     * @param SubscriberResource $subscriberResource
     */
    public function __construct(
        protected Http $request,
        protected SubscriberFactory $subscriberFactory,
        protected StoreManagerInterface $storeManager,
        protected SubscriberResource $subscriberResource,
        protected CustomerRepositoryInterface $customerRepository
    ) {}

    /**
     * Arroun plugin for suscribe function
     *
     * @param SubscriptionManager $subject
     * @param callable $proceed
     * @param string $email
     * @param string $storeId
     */
    public function aroundSubscribe(SubscriptionManager $subject, callable $proceed, $email, $storeId)
    {
        $params = $this->request->getParams();
        $resultValidate = $this->validateParams($params);
        if ($this->request->isPost() && $resultValidate['status']) {

            $result = $proceed($email, $storeId);

            $subscriberName = $params['firstname'];
            $subscriberLastname = $params['lastname'];
            $subscriberGender = $params['gender'];
            $subscriberDob = $params['dob'];
            $websiteId = (int)$this->storeManager->getStore($storeId)->getWebsiteId();
            $subscriber = $this->subscriberFactory->create()->loadBySubscriberEmail($email, $websiteId);

            if ($subscriber->getId()) {
                $subscriber->setData('first_name', $subscriberName);
                $subscriber->setData('last_name', $subscriberLastname);
                $subscriber->setData('gender', $subscriberGender);
                $subscriber->setData('dob', $subscriberDob);
                $this->subscriberResource->save($subscriber);
            }
        } else if ($resultValidate['status']) {
            throw new \Exception($resultValidate['message']);
        }
        return $result;
    }

    /**
     * Validate params function
     *
     * @param array $param
     * @return array $resultValidate
     */
    private function validateParams(array $params): array
    {
        $resultValidate = [
            'status' => true,
            'message' => ''
        ];

        if (empty($params)) {
            $resultValidate['status'] = false;
            $resultValidate['message'] = 'You have not entered the form data.';
        }

        foreach ($params as $key => $param) {
            if (!$param) {
                $resultValidate['status'] = false;
                $resultValidate['message'] = 'You have not entered field ${key}';
            }
        }

        return $resultValidate;
    }

    /**
     * Before subscribeCustomer plugin to register for customers
     *
     * @param SubscriptionManager $subject
     * @param int $customerId
     * @param int $storeId
     * @return mixed
     */
    public function aroundSubscribeCustomer(SubscriptionManager $subject, callable $proceed, int $customerId, int $storeId): mixed
    {
        $params = $this->request->getParams();
        $resultValidate = $this->validateParams($params);
        $result = $proceed($customerId, $storeId);

        if ($this->request->isPost() && $resultValidate['status']) {

            $subscriberName = $params['firstname'];
            $subscriberLastname = $params['lastname'];
            $subscriberGender = $params['gender'];
            $subscriberDob = $params['dob'];
            $websiteId = (int)$this->storeManager->getStore($storeId)->getWebsiteId();
            $customer = $this->customerRepository->getById($customerId);
            $subscriber = $this->subscriberFactory->create()->loadBySubscriberEmail($customer->getEmail(), $websiteId);

            if ($subscriber->getId()) {
                $subscriber->setData('first_name', $subscriberName);
                $subscriber->setData('last_name', $subscriberLastname);
                $subscriber->setData('gender', $subscriberGender);
                $subscriber->setData('dob', $subscriberDob);
                $this->subscriberResource->save($subscriber);
            }
        }
        return $result;
    }
}
