<?php
/*
 *   <AUTHOR> <<EMAIL>> || OmniPro Team
 *   @copyright Copyright (c) 2024 OmniPro (https://omni.pro/)
 *   @Date 25/04/2024
 *   @package Omnipro_CustomerOrdersHistory
*/

namespace Omnipro\CustomerOrdersHistory\Plugin;

use Magento\Customer\Model\Session;
use Magento\Store\Model\StoreManagerInterface;
use Omnipro\CustomerOrdersHistory\Api\ConfigInterface;

class Quote
{
    /**
     * Quote constructor
     * @param ConfigInterface $config
     * @param StoreManagerInterface $storeManager
     * @param Session $customerSession
     */
    public function __construct(
        protected ConfigInterface $config,
        protected StoreManagerInterface $storeManager,
        protected Session $customerSession
    ) {
    }

    /**
     * Plugin after beforeSave
     * Set quote with customer data if exist session because guest quote in checkout #shipping 
     * doesn't show delivery methods
     * @return Magento\Quote\Model\Quote $result
     */
    public function afterBeforeSave(
        \Magento\Quote\Model\Quote $subject,
        $result
    ) {
        if ($this->config->getEnableModule()) {
            if ($this->customerSession->getCustomerId()) {
                if ($result->getCustomerIsGuest() || empty($result->getCustomerId())) {
                    $result->setCustomerIsGuest(0);
                    $result->setCustomerId($this->customerSession->getCustomerId());
                    $result->setCustomerEmail($this->customerSession->getCustomer()->getEmail());
                }
            }
        }
        return $result;
    }
}
