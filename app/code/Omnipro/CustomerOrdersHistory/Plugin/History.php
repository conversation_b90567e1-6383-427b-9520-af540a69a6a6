<?php
/*
 *   <AUTHOR> <<EMAIL>> || OmniPro Team
 *   @copyright Copyright (c) 2024 OmniPro (https://omni.pro/)
 *   @Date 25/04/2024
 *   @package Omnipro_CustomerOrdersHistory
*/

namespace Omnipro\CustomerOrdersHistory\Plugin;

use Magento\Customer\Model\Session;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactoryInterface;
use Magento\Store\Model\StoreManagerInterface;
use Omnipro\CustomerOrdersHistory\Api\ConfigInterface;

class History
{
    /**
     * History constructor
     * @param CollectionFactoryInterface $orderCollectionFactory
     * @param StoreManagerInterface $storeManager
     * @param Session $customerSession
     * @param ConfigInterface $config
     */
    public function __construct(
        protected CollectionFactoryInterface $orderCollectionFactory,
        protected StoreManagerInterface $storeManager,
        protected Session $customerSession,
        protected ConfigInterface $config
    ) {
    }

    /**
     * Plugin after getOrders
     * Filter orders by customer email for get order as guest with the same email
     * @param $result
     * @return bool|\Magento\Sales\Model\ResourceModel\Order\Collection
     */
    public function afterGetOrders(
        \Magento\Sales\Block\Order\History $subject,
        $result
    ) {
        if ($this->config->getEnableModule($this->storeManager->getWebsite()->getId())) {
            $customerEmail = $this->customerSession->getCustomerData()->getEmail();
            $storeId = $this->storeManager->getStore()->getId();
            $orders = $this->orderCollectionFactory->create()->addFieldToSelect(
                '*'
            )->addFieldToFilter(
                'customer_email',
                ['eq' => $customerEmail]
            )->addFieldToFilter(
                'store_id',
                ['eq' => $storeId]
            )->setOrder(
                'created_at',
                'desc'
            );
            $result = $orders;
        }
        return $result;
    }
}
