<?php
/*
 *   <AUTHOR> <<EMAIL>> || OmniPro Team
 *   @copyright Copyright (c) 2024 OmniPro (https://omni.pro/)
 *   @Date 25/04/2024
 *   @package Omnipro_CustomerOrdersHistory
*/

namespace Omnipro\CustomerOrdersHistory\Plugin;

use Magento\Customer\Model\Session;
use Magento\Sales\Model\Order\Config;
use Omnipro\CustomerOrdersHistory\Api\ConfigInterface;

class OrderViewAuthorization
{
    /**
     * OrderViewAuthorization constructor
     * @param Session $customerSession
     * @param Config $orderConfig
     * @param ConfigInterface $config
     */
    public function __construct(
        protected Session $customerSession,
        protected Config $orderConfig,
        protected ConfigInterface $config
    ) {
    }

    /**
     * Plugin after canView
     * To allow view order and reorder guest orders
     * @param Magento\Sales\Model\Order $order
     * @return bool
     */
    public function afterCanView(
        \Magento\Sales\Controller\AbstractController\OrderViewAuthorization $subject,
        $result,
        $order
    ) {
        if ($this->config->getEnableModule()) {
            $customerEmail = $this->customerSession->getCustomer()->getEmail();
            $availableStatuses = $this->orderConfig->getVisibleOnFrontStatuses();
            if (
                $order->getId()
                && $order->getCustomerEmail() == $customerEmail
                && in_array($order->getStatus(), $availableStatuses, true)
            ) {
                $result = true;
            } else {
                $result = false;
            }
        }
        return $result;
    }
}
