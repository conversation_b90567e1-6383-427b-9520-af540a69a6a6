<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Omnipro\CustomerOrdersHistory\Api\ConfigInterface" type="Omnipro\CustomerOrdersHistory\Model\Config" />
    <type name="Magento\Sales\Block\Order\History">
		<plugin name="Omnipro_Plugin_Order_History" type="Omnipro\CustomerOrdersHistory\Plugin\History" sortOrder="10" disabled="false"/>
	</type>
	<type name="Magento\Sales\Controller\AbstractController\OrderViewAuthorization">
		<plugin name="Omnipro_Plugin_AbstractController_OrderViewAuthorization" type="Omnipro\CustomerOrdersHistory\Plugin\OrderViewAuthorization" sortOrder="10" disabled="false"/>
	</type>
	<type name="Magento\Quote\Model\Quote">
		<plugin name="Omnipro_Plugin_Model_Quote" type="Omnipro\CustomerOrdersHistory\Plugin\Quote" sortOrder="10" disabled="false"/>
	</type>
</config>
