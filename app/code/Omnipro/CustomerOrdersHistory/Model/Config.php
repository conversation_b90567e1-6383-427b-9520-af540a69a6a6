<?php
/**
 * Copyright (c) 2023 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> <<EMAIL>> | OmniPro Team
 * @date 26/04/24
 * @category Omnipro
 * @module Omnipro/CustomerOrdersHistory
 */

declare(strict_types=1);

namespace Omnipro\CustomerOrdersHistory\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Omnipro\CustomerOrdersHistory\Api\ConfigInterface;

class Config implements ConfigInterface
{
    /**
     * Constant for order history enable config path
     */
    private const XML_PATH_ENABLE_MODULE = 'omnipro_customer_orders_history/general/enable';

    /**
     * Config constructor.
     *
     * @param ScopeConfigInterface $scopeConfig
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        private ScopeConfigInterface $scopeConfig,
        private StoreManagerInterface $storeManager
    ) {
    }

    /**
     * Get the website id for the provided store id.
     *
     * @param int|string|null $storedId
     * @return int|string
     */
    public function getWebsiteId(int|string $websiteId = null): int|string
    {
        try {
            return $websiteId ?? $this->storeManager->getWebsite()->getId();
        } catch (NoSuchEntityException $e) {
            return '';
        }
    }

    /**
     * @inheritdoc
     */
    public function getEnableModule(?int $websiteId = null): string
    {
        return (string) $this->scopeConfig->getValue(
            self::XML_PATH_ENABLE_MODULE,
            ScopeInterface::SCOPE_WEBSITES,
            $this->getWebsiteId($websiteId)
        );
    }
}
