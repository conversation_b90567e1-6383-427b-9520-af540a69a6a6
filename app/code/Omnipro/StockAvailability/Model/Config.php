<?php
/**
 * Created on Tue Jun 18 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\Model
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
 */
declare(strict_types=1);

namespace Omnipro\StockAvailability\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Omnipro\StockAvailability\Api\ConfigInterface;

/**
 * Config Model
 */
class Config implements ConfigInterface
{
    /**
     * Constants for configuration paths
     *
     * @var string
     */
    private const XML_PATH_CONFIG_ENABLED = 'omnipro_stock_availability/general/enable';
    private const XML_PATH_CONFIG_ENABLED_HOME_DELIVERY = 'omnipro_stock_availability/general/enable_home_delivery';
    private const XML_PATH_CONFIG_ENABLED_STORE_PICKUP = 'omnipro_stock_availability/general/enable_store_pickup';
    private const XML_PATH_CONFIG_ATTRIBUTE_SIZE_CODE = 'omnipro_stock_availability/general/attribute_size_code';
    private const XML_PATH_CONFIG_ATTRIBUTE_COLOR_CODE = 'omnipro_stock_availability/general/attribute_color_code';
    private const XML_PATH_CONFIG_TOTAL_STOCK = 'omnipro_stock_availability/general/total_stock';

    /**
     * Constructor
     *
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        private ScopeConfigInterface $scopeConfig
    ) {
    }

    /**
     * @inheritDoc
     */
    public function isEnable(int $storeId): ?bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_CONFIG_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @inheritDoc
     */
    public function isEnableHomeDelivery(int $storeId): ?bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_CONFIG_ENABLED_HOME_DELIVERY,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @inheritDoc
     */
    public function isEnableStorePickup(int $storeId): ?bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_CONFIG_ENABLED_STORE_PICKUP,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @inheritDoc
     */
    public function getAttributeSizeCode(int $storeId): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_CONFIG_ATTRIBUTE_SIZE_CODE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @inheritDoc
     */
    public function getAttributeColorCode(int $storeId): ?string
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_CONFIG_ATTRIBUTE_COLOR_CODE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @inheritDoc
     */
    public function isEnableTotalStock(int $storeId): ?bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_CONFIG_TOTAL_STOCK,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
