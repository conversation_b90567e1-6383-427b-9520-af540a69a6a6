<?php
/**
 * Created on Fri Jun 14 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\Model\Data
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
 */
declare(strict_types=1);

namespace Omnipro\StockAvailability\Model\Data;

use Magento\Framework\DataObject;
use Omnipro\StockAvailability\Api\Data\AvailabilityItemStockInterface;

/**
 * Availability Item Stock Model
 */
class AvailabilityItemStock extends DataObject implements AvailabilityItemStockInterface
{
    /**
     * @inheritDoc
     */
    public function getSourceCode(): ?string
    {
        return $this->getData(self::SOURCE_CODE);
    }

    /**
     * @inheritDoc
     */
    public function setSourceCode(string $sourceCode): AvailabilityItemStockInterface
    {
        return $this->setData(self::SOURCE_CODE, $sourceCode);
    }

    /**
     * @inheritDoc
     */
    public function getSourceName(): ?string
    {
        return $this->getData(self::SOURCE_NAME);
    }

    /**
     * @inheritDoc
     */
    public function setSourceName(string $sourceName): AvailabilityItemStockInterface
    {
        return $this->setData(self::SOURCE_NAME, $sourceName);
    }

    /**
     * @inheritDoc
     */
    public function getIsStorePickup(): ?bool
    {
        return $this->getData(self::IS_STORE_PICKUP);
    }

    /**
     * @inheritDoc
     */
    public function setIsStorePickup(bool $isStorePickup): AvailabilityItemStockInterface
    {
        return $this->setData(self::IS_STORE_PICKUP, $isStorePickup);
    }

    /**
     * @inheritDoc
     */
    public function getQuantity(): ?int
    {
        return $this->getData(self::QUANTITY);
    }

    /**
     * @inheritDoc
     */
    public function setQuantity(int $quantity): AvailabilityItemStockInterface
    {
        return $this->setData(self::QUANTITY, $quantity);
    }
}
