<?php
/**
 * Created on Fri Jun 14 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\Model\Data
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
 */
declare(strict_types=1);

namespace Omnipro\StockAvailability\Model\Data;

use Magento\Framework\DataObject;
use Omnipro\StockAvailability\Api\Data\AvailabilityItemInterface;

/**
 * Availability Item Model
 */
class AvailabilityItem extends DataObject implements AvailabilityItemInterface
{
    /**
     * @inheritDoc
     */
    public function getProductId(): ?int
    {
        return $this->getData(self::PRODUCT_ID);
    }

    /**
     * @inheritDoc
     */
    public function setProductId(int $productId): AvailabilityItemInterface
    {
        return $this->setData(self::PRODUCT_ID, $productId);
    }

    /**
     * @inheritDoc
     */
    public function getProductSku(): ?string
    {
        return $this->getData(self::PRODUCT_SKU);
    }

    /**
     * @inheritDoc
     */
    public function setProductSku(string $productSku): AvailabilityItemInterface
    {
        return $this->setData(self::PRODUCT_SKU, $productSku);
    }

    /**
     * @inheritDoc
     */
    public function getProductSize(): ?string
    {
        return $this->getData(self::PRODUCT_SIZE);
    }

    /**
     * @inheritDoc
     */
    public function setProductSize(string $productSize): AvailabilityItemInterface
    {
        return $this->setData(self::PRODUCT_SIZE, $productSize);
    }

    /**
     * @inheritDoc
     */
    public function getProductColor(): ?string
    {
        return $this->getData(self::PRODUCT_COLOR);
    }

    /**
     * @inheritDoc
     */
    public function setProductColor(string $productColor): AvailabilityItemInterface
    {
        return $this->setData(self::PRODUCT_COLOR, $productColor);
    }

    /**
     * @inheritDoc
     */
    public function getStock(): ?array
    {
        return $this->getData(self::STOCK);
    }

    /**
     * @inheritDoc
     */
    public function setStock(array $stock): AvailabilityItemInterface
    {
        return $this->setData(self::STOCK, $stock);
    }
}
