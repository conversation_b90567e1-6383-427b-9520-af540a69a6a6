<?php
/**
 * Created on Mon Jun 17 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\Model\Management
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
 */
declare(strict_types=1);

namespace Omnipro\StockAvailability\Model\Management;

use Infinite\ExtendInventoryReservations\Service\ReservationService;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\LocalizedException;
use Omnipro\ShippingByStock\Api\ConfigInterface as ShippingByStockConfigInterface;
use Magento\Store\Model\StoreManagerInterface;
use Omnipro\StockAvailability\Api\ConfigInterface;
use Omnipro\StockAvailability\Api\Data\AvailabilityItemInterfaceFactory;
use Omnipro\StockAvailability\Api\Data\AvailabilityItemStockInterfaceFactory;
use Omnipro\StockAvailability\Api\Management\AvailabilityItemInfoInterface;

/**
 * Availability Item Info Implementation
 */
class AvailabilityItemInfo implements AvailabilityItemInfoInterface
{
    /**
     * Constructor
     *
     * @param ResourceConnection $resourceConnection
     * @param StoreManagerInterface $storeManager
     * @param ConfigInterface $config
     * @param AvailabilityItemInterfaceFactory $availabilityItemInterfaceFactory
     * @param AvailabilityItemStockInterfaceFactory $availabilityItemStockInterfaceFactory
     * @param ReservationService $reservationService
     * @param ShippingByStockConfigInterface $shippingByStockConfig
     */
    public function __construct(
        private ResourceConnection $resourceConnection,
        private StoreManagerInterface $storeManager,
        private ConfigInterface $config,
        private AvailabilityItemInterfaceFactory $availabilityItemInterfaceFactory,
        private AvailabilityItemStockInterfaceFactory $availabilityItemStockInterfaceFactory,
        private ReservationService $reservationService,
        private ShippingByStockConfigInterface $shippingByStockConfig
    ) {
    }

    /**
     * @inheritDoc
     */
    public function execute(int $productId): array
    {
        $items = [];
        $stocks = $this->fetchStock($productId);

        foreach ($stocks as $stock) {
            $item = $this->availabilityItemInterfaceFactory->create();
            $item->setProductId((int)$stock['simple_entity_id']);
            $item->setProductSku($stock['simple_sku']);
            $item->setProductSize($stock['simple_size']);
            $item->setProductColor($stock['simple_color']);

            $stocks = [];
            foreach ($stock['stocks'] as $stockItem) {
                $stock = $this->availabilityItemStockInterfaceFactory->create();
                $stock->setSourceCode($stockItem['source_code']);
                $stock->setSourceName($stockItem['source_name']);
                $stock->setIsStorePickup((bool)$stockItem['source_pickup_location']);
                $stock->setQuantity((int)$stockItem['quantity']);
                $stocks[] = $stock;
            }

            $item->setStock($stocks);
            $items[] = $item;
        }

        return $items;
    }

    /**
     * Fetch stock
     *
     * @param int $productId
     * @return array
     * @throws LocalizedException
     */
    private function fetchStock(int $productId): array
    {
        $result = [];

        $sourceItems = $this->getSourceItems($productId);
        $sourceItemsWithReservations = $this->getSourceItemsWithReservationsQty($sourceItems);

        foreach ($sourceItemsWithReservations as $sourceItem) {

            if (!isset($result[$sourceItem['simple_entity_id']])) {
                if ($sourceItem['quantity'] > 0) {
                    $result[$sourceItem['simple_entity_id']] = [
                        'simple_entity_id' => $sourceItem['simple_entity_id'],
                        'simple_sku' => $sourceItem['simple_sku'],
                        'simple_size' => $sourceItem['size_label'],
                        'simple_color' => $sourceItem['color_label'],
                        'stocks' => [
                            [
                                'source_code' => $sourceItem['source_code'],
                                'source_name' => $sourceItem['source_name'],
                                'source_pickup_location' => $sourceItem['source_pickup_location'],
                                'quantity' => $sourceItem['quantity']
                            ]
                        ]
                    ];
                }
            } else {
                if ($sourceItem['quantity'] > 0) {
                    $result[$sourceItem['simple_entity_id']]['stocks'][] = [
                        'source_code' => $sourceItem['source_code'],
                        'source_name' => $sourceItem['source_name'],
                        'source_pickup_location' => $sourceItem['source_pickup_location'],
                        'quantity' => $sourceItem['quantity']
                    ];
                }
            }
        }

        return $result;
    }

    /**
     * Obtiene los elementos de stock para un producto dado, filtrando por el website actual.
     *
     * @param int $productId
     * @return array
     */
    private function getSourceItems(int $productId): array
    {
        $storeId = (int)$this->storeManager->getStore()->getId();
        $sizeAttributeCode = $this->config->getAttributeSizeCode($storeId);
        $colorAttributeCode = $this->config->getAttributeColorCode($storeId);
        $websiteCode = $this->storeManager->getWebsite()->getCode();

        $connection = $this->resourceConnection->getConnection();
        $select = $connection->select()
            ->from(
                ['cpe' => $this->resourceConnection->getTableName('catalog_product_entity')],
                ['configurable_entity_id' => 'entity_id', 'configurable_sku' => 'sku']
            )
            ->join(
                ['cpr' => $this->resourceConnection->getTableName('catalog_product_relation')],
                'cpr.parent_id = cpe.row_id',
                []
            )
            ->join(
                ['cpe2' => $this->resourceConnection->getTableName('catalog_product_entity')],
                'cpr.child_id = cpe2.entity_id',
                ['simple_entity_id' => 'entity_id', 'simple_sku' => 'sku']
            )
            ->join(
                ['cpei' => $this->resourceConnection->getTableName('catalog_product_entity_int')],
                'cpei.row_id = cpe2.row_id',
                []
            )
            ->join(
                ['ea' => $this->resourceConnection->getTableName('eav_attribute')],
                'ea.attribute_id = cpei.attribute_id',
                []
            )
            ->join(
                ['eaov' => $this->resourceConnection->getTableName('eav_attribute_option_value')],
                'cpei.value = eaov.option_id',
                ['size_label' => 'value']
            )
            ->join(
                ['cpei2' => $this->resourceConnection->getTableName('catalog_product_entity_int')],
                'cpei2.row_id = cpe2.row_id',
                []
            )
            ->join(
                ['ea2' => $this->resourceConnection->getTableName('eav_attribute')],
                'ea2.attribute_id = cpei2.attribute_id',
                []
            )
            ->join(
                ['eaov2' => $this->resourceConnection->getTableName('eav_attribute_option_value')],
                'cpei2.value = eaov2.option_id',
                ['color_label' => 'value']
            )
            ->join(
                ['isi' => $this->resourceConnection->getTableName('inventory_source_item')],
                'cpe2.sku = isi.sku',
                ['source_code', 'quantity']
            )
            ->join(
                ['is2' => $this->resourceConnection->getTableName('inventory_source')],
                'isi.source_code = is2.source_code',
                ['source_name' => 'name', 'source_pickup_location' => 'is_pickup_location_active']
            )
            ->join(
                ['ssl' => $this->resourceConnection->getTableName('inventory_source_stock_link')],
                'isi.source_code = ssl.source_code',
                []
            )
            ->join(
                ['sc' => $this->resourceConnection->getTableName('inventory_stock_sales_channel')],
                'ssl.stock_id = sc.stock_id',
                []
            )
            ->where('sc.type = ?', 'website')
            ->where('sc.code = ?', $websiteCode)
            ->where('cpe.entity_id = ?', $productId)
            ->where('cpe.type_id = ?', 'configurable')
            ->where('ea.attribute_code = ?', $sizeAttributeCode)
            ->where('cpei.store_id = ?', 0)
            ->where('eaov.store_id = ?', 0)
            ->where('ea2.attribute_code = ?', $colorAttributeCode)
            ->where('cpei2.store_id = ?', 0)
            ->where('eaov2.store_id = ?', 0)
            ->where('is2.enabled = ?', 1)
            ->where('is2.source_code != ?', 'default')
            ->where('isi.quantity > 0')
            ->order('eaov.value');

        return $connection->fetchAll($select);
    }

    /**
     * Get products without stock
     *
     * @param array $sourceItems
     * @return array
     * @throws LocalizedException
     */
    private function getSourceItemsWithReservationsQty(array $sourceItems): array
    {
        $newSourceItems = [];
        $homeDeliveryStockBySku = [];
        $homeDeliveryReservationsBySku = [];
        $homeDeliverySourceTemplate = [];
        $storeId = (int)$this->storeManager->getStore()->getId();
        $validateShippingType = $this->shippingByStockConfig->isEnableValidateShipping($storeId);
        $isEnableTotalStock = $this->config->isEnableTotalStock($storeId);

        foreach ($sourceItems as $sourceItem) {
            if (!isset($sourceItem['simple_sku'])) {
                continue;
            }

            $sku = $sourceItem['simple_sku'];
            $sourceCode = $sourceItem['source_code'];
            $shippingType = $sourceItem['source_pickup_location'] == '1' ?
                ReservationService::SHIPPING_TYPE_STORE_PICKUP : ReservationService::SHIPPING_TYPE_HOME_DELIVERY;

            $qtyReserved = $this->reservationService->getReservationsBySkuAndSource(
                $sku,
                $validateShippingType,
                $shippingType,
                $sourceCode
            );
            $availableQty = $sourceItem['quantity'] + $qtyReserved;

            if ($shippingType === 'pickup') {
                if ($availableQty > 0) {
                    $sourceItem['quantity'] = $availableQty;
                    $newSourceItems[] = $sourceItem;
                }
                if ($isEnableTotalStock) {
                    if (!isset($homeDeliveryStockBySku[$sku])) {
                        $homeDeliveryStockBySku[$sku] = 0;
                        $homeDeliveryReservationsBySku[$sku] = 0;
                        $homeDeliverySourceTemplate[$sku] = $sourceItem;
                    }
                    $homeDeliveryStockBySku[$sku] += $availableQty;
                    $homeDeliveryReservationsBySku[$sku] += $qtyReserved;
                }
            } else {
                if (!isset($homeDeliveryStockBySku[$sku])) {
                    $homeDeliveryStockBySku[$sku] = 0;
                    $homeDeliveryReservationsBySku[$sku] = 0;
                    $homeDeliverySourceTemplate[$sku] = $sourceItem;
                }

                $homeDeliveryStockBySku[$sku] += $availableQty;
                $homeDeliveryReservationsBySku[$sku] += $qtyReserved;
            }
        }

        foreach ($homeDeliveryStockBySku as $sku => $totalStock) {
            $totalReservations = $homeDeliveryReservationsBySku[$sku];
            $finalStock = $totalStock + $totalReservations;

            if ($finalStock > 0) {
                $homedeliveryItem = $homeDeliverySourceTemplate[$sku];
                $homedeliveryItem['source_code'] = 'homedelivery';
                $homedeliveryItem['source_name'] = 'Home Delivery';
                $homedeliveryItem['source_pickup_location'] = '0';
                $homedeliveryItem['quantity'] = $finalStock;

                $newSourceItems[] = $homedeliveryItem;
            }
        }

        return $newSourceItems;
    }
}
