<?php
/**
 * Created on Tue Jun 18 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\Api
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
 */
namespace Omnipro\StockAvailability\Api;

/**
 * Config Definition
 */
interface ConfigInterface
{
    /**
     * Check if the module is enabled
     *
     * @param int $storeId
     * @return bool|null
     */
    public function isEnable(int $storeId): ?bool;

    /**
     * Check if the home delivery is enabled
     *
     * @param int $storeId
     * @return bool|null
     */
    public function isEnableHomeDelivery(int $storeId): ?bool;

    /**
     * Check if the store pickup is enabled
     *
     * @param int $storeId
     * @return bool|null
     */
    public function isEnableStorePickup(int $storeId): ?bool;

    /**
     * Get the attribute size code
     *
     * @param int $storeId
     * @return string|null
     */
    public function getAttributeSizeCode(int $storeId): ?string;

    /**
     * Get the attribute color code
     *
     * @param int $storeId
     * @return string|null
     */
    public function getAttributeColorCode(int $storeId): ?string;

    /**
     * Check if the home delivery is enabled total stock
     *
     * @param int $storeId
     * @return bool|null
     */
    public function isEnableTotalStock(int $storeId): ?bool;
}
