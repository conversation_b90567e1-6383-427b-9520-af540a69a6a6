<?php
/**
 * Created on Fri Jun 14 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\Api\Data
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
 */
namespace Omnipro\StockAvailability\Api\Data;

/**
 * Availability Item Stock Interface Definition
 */
interface AvailabilityItemStockInterface
{
    /**
     * Constants for keys of data array. Identical to the name of the getter in snake case
     *
     * @var string
     */
    public const SOURCE_CODE = 'source_code';
    public const SOURCE_NAME = 'source_name';
    public const IS_STORE_PICKUP = 'is_store_pickup';
    public const QUANTITY = 'quantity';

    /**
     * Getter for source code
     *
     * @return string|null
     */
    public function getSourceCode(): ?string;

    /**
     * Setter for source code
     *
     * @param string $sourceCode
     * @return AvailabilityItemStockInterface
     */
    public function setSourceCode(string $sourceCode): AvailabilityItemStockInterface;

    /**
     * Getter for source name
     *
     * @return string|null
     */
    public function getSourceName(): ?string;

    /**
     * Setter for source name
     *
     * @param string $sourceName
     * @return AvailabilityItemStockInterface
     */
    public function setSourceName(string $sourceName): AvailabilityItemStockInterface;

    /**
     * Getter for is store pickup
     *
     * @return bool|null
     */
    public function getIsStorePickup(): ?bool;

    /**
     * Setter for is store pickup
     *
     * @param bool $isStorePickup
     * @return AvailabilityItemStockInterface
     */
    public function setIsStorePickup(bool $isStorePickup): AvailabilityItemStockInterface;

    /**
     * Getter for quantity
     *
     * @return int|null
     */
    public function getQuantity(): ?int;

    /**
     * Setter for quantity
     *
     * @param int $quantity
     * @return AvailabilityItemStockInterface
     */
    public function setQuantity(int $quantity): AvailabilityItemStockInterface;
}
