<?php
/**
 * Created on Fri Jun 14 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\Api\Data
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
 */
namespace Omnipro\StockAvailability\Api\Data;

/**
 * Availability Item Interface Definition
 */
interface AvailabilityItemInterface
{
    /**
     * Constants for keys of data array. Identical to the name of the getter in snake case
     *
     * @var string
     */
    public const PRODUCT_ID = 'product_id';
    public const PRODUCT_SKU = 'product_sku';
    public const PRODUCT_SIZE = 'product_size';
    public const PRODUCT_COLOR = 'product_color';
    public const STOCK = 'stock';
    
    /**
     * Getter for Product Id
     *
     * @return int|null
     */
    public function getProductId(): ?int;

    /**
     * Setter for Product Id
     *
     * @param int $productId
     * @return AvailabilityItemInterface
     */
    public function setProductId(int $productId): AvailabilityItemInterface;

    /**
     * Getter for Product SKU
     *
     * @return string|null
     */
    public function getProductSku(): ?string;

    /**
     * Setter for Product SKU
     *
     * @param string $productSku
     * @return AvailabilityItemInterface
     */
    public function setProductSku(string $productSku): AvailabilityItemInterface;

    /**
     * Getter for Product Size
     *
     * @return string|null
     */
    public function getProductSize(): ?string;

    /**
     * Setter for Product Size
     *
     * @param string $productSize
     * @return AvailabilityItemInterface
     */
    public function setProductSize(string $productSize): AvailabilityItemInterface;

    /**
     * Getter for Product Color
     *
     * @return string|null
     */
    public function getProductColor(): ?string;

    /**
     * Setter for Product Color
     *
     * @param string $productColor
     * @return AvailabilityItemInterface
     */
    public function setProductColor(string $productColor): AvailabilityItemInterface;

    /**
     * Getter for Stock
     *
     * @return \Omnipro\StockAvailability\Api\Data\AvailabilityItemStockInterface[]|null
     */
    public function getStock(): ?array;

    /**
     * Setter for Stock
     *
     * @param \Omnipro\StockAvailability\Api\Data\AvailabilityItemStockInterface[] $stock
     * @return AvailabilityItemInterface
     */
    public function setStock(array $stock): AvailabilityItemInterface;
}
