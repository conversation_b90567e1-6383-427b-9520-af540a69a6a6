<?php
/**
 * Created on Mon Jun 17 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\Api\Management
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
 */
namespace Omnipro\StockAvailability\Api\Management;

/**
 * Availability Item Info Interface Definition
 */
interface AvailabilityItemInfoInterface
{
    /**
     * Get Availability Item Info
     *
     * @param int $productId
     * @return \Omnipro\StockAvailability\Api\Data\AvailabilityItemInterface[]
     * @throws \Magento\Framework\Exception\LocalizedException|\Exception
     */
    public function execute(int $productId): array;
}
