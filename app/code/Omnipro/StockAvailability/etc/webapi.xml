<?xml version="1.0"?>
<!--
/**
 * Created on Mon Jun 17 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\etc
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
*/
-->
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/stock-availability/:product_id" method="GET">
        <service class="Omnipro\StockAvailability\Api\Management\AvailabilityItemInfoInterface" method="execute"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
</routes>
