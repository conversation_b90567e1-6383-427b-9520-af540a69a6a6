<?xml version="1.0"?>
<!--
/**
 * Created on Fri Jun 14 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\etc\adminhtml
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="omnipro_stock_availability" translate="label" sortOrder="130" showInDefault="1"
            showInWebsite="1" showInStore="1">
            <class>separator-top</class>
            <label>Stock Availability</label>
            <tab>omnipro</tab>
            <resource>Omnipro_StockAvailability::config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1"
                showInWebsite="1" showInStore="1">
                <label>General Configuration</label>
                <field id="enable" translate="label" type="select" sortOrder="1" showInDefault="1"
                    showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Module Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="enable_home_delivery" translate="label" type="select" sortOrder="2"
                    showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enable Info Home Delivery</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="enable_store_pickup" translate="label" type="select" sortOrder="3"
                    showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Enable Info Store Pickup</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="attribute_size_code" translate="label" type="text" sortOrder="4"
                    showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Attribute Size Code / EAV Int</label>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="attribute_color_code" translate="label" type="text" sortOrder="4"
                    showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Attribute Color Code / EAV Int</label>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="total_stock" translate="label" type="select" sortOrder="4"
                       showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Calculate Total Stock for Home Delivery</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                    <comment>Enable this to calculate stock for Home Delivery including instore pickup stock</comment>
                </field>
            </group>
        </section>
    </system>
</config>
