<?php
/**
 * Created on Tue Jun 18 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\ViewModel
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
 */
declare(strict_types=1);

namespace Omnipro\StockAvailability\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Store\Model\StoreManagerInterface;
use Omnipro\StockAvailability\Api\ConfigInterface;

/**
 * Stock Availability ViewModel
 */
class StockAvailability implements ArgumentInterface
{
    /**
     * Constructor
     *
     * @param StoreManagerInterface $storeManager
     * @param ConfigInterface $config
     */
    public function __construct(
        private StoreManagerInterface $storeManager,
        private ConfigInterface $config
    ) {
    }

    /**
     * Check if the module is enabled
     *
     * @return bool
     */
    public function isEnable(): bool
    {
        $storeId = (int)$this->storeManager->getStore()->getId();

        return $this->config->isEnable($storeId);
    }

    /**
     * Check if the home delivery is enabled
     *
     * @return bool
     */
    public function isEnableHomeDelivery(): bool
    {
        $storeId = (int)$this->storeManager->getStore()->getId();

        return $this->config->isEnableHomeDelivery($storeId);
    }

    /**
     * Check if the store pickup is enabled
     *
     * @return bool
     */
    public function isEnableStorePickup(): bool
    {
        $storeId = (int)$this->storeManager->getStore()->getId();

        return $this->config->isEnableStorePickup($storeId);
    }
}
