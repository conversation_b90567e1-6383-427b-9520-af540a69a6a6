<?php
/**
 * Created on Tue Jun 18 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\view\frontend\templates
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
 */
/**
 * @var \Magento\Catalog\Block\Product\View $block
 * @var \Omnipro\StockAvailability\ViewModel\StockAvailability $viewModelStockAvailability
 */
$viewModelStockAvailability = $block->getData('view_model_stock_availability');

$isEnable = $viewModelStockAvailability->isEnable();
$isEnableHomeDelivery = $viewModelStockAvailability->isEnableHomeDelivery();
$isEnableStorePickup = $viewModelStockAvailability->isEnableStorePickup();

$product = $block->getProduct();
$productId = $product->getId();
?>
<?php if ($isEnable): ?>
    <div id="productStockAvailability"
         class="product-stock-availability-box" 
         data-bind="scope: 'productStockAvailability'">
        <!-- ko template: getTemplate() --> <!-- /ko -->
    </div>
    <script type="text/x-magento-init">
        {
            "#productStockAvailability": {
                "Magento_Ui/js/core/app": {
                    "components": {
                        "productStockAvailability": {
                            "component": "Omnipro_StockAvailability/js/product/stock-availability",
                            "template" : "Omnipro_StockAvailability/product/stock-availability",
                            "productId": "<?= /* @noEscape */ $productId ?>",
                            "storePickupAvailabilityEnabled": "<?= /* @noEscape */ $isEnableStorePickup ?>",
                            "homeDeliveryAvailabilityEnabled": "<?= /* @noEscape */ $isEnableHomeDelivery ?>"
                        }
                    }
                }
            }
        }
    </script>
<?php endif; ?>
