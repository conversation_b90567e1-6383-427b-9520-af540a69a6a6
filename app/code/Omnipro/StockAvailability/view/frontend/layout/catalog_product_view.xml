<?xml version="1.0"?>
<!--
/**
 * Created on Fri Jun 14 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\StockAvailability\view\frontend\layout
 * @category Omnipro
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
*/
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="product.info.main">
            <block class="Magento\Catalog\Block\Product\View" name="product.view.storepickup"
                after="-"
                template="Omnipro_StockAvailability::product/view/stock-availability.phtml">
                <arguments>
                    <argument name="view_model_stock_availability" xsi:type="object">Omnipro\StockAvailability\ViewModel\StockAvailability</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>