define([
    'uiComponent',
    'ko',
    'jquery',
    'underscore',
    'mage/url',
    'mage/storage',
    'mage/translate',
    'mage/collapsible'
], function (Component, ko, $, _, urlBuilder, storage, $t) {
    'use strict';

    return Component.extend({
        defaults: {
            stockInfo: [],
            isStorePickup: false,
            isLoading: false,
            sizesForPickup: [],
            sizesForDelivery: []
        },

        /**
         * Initializes the component
         */
        initialize: function () {
            this._super();
        },

        /**
         * Initializes observable properties to make them responsive to UI changes.
         *
         * @returns {Component} Chainable.
         */
        initObservable: function () {
            this._super().observe(['stockInfo', 'isStorePickup', 'isLoading', 'sizesForPickup', 'sizesForDelivery']);
            return this;
        },

        /**
         * Applies collapsible panel functionality to an element.
         *
         * @param {HTMLElement} element The element to be made collapsible.
         */
        applyCollapsible: function(element) {
            $(element).collapsible({
                active: false,
                animate: { duration: 400, easing: 'easeOutCubic' },
                header: '.options-availability.header',
                content: '.options-availability.content',
                openedState: 'active'
            });
        },

        /**
         * Triggers the stock availability check and displays the modal.
         *
         * @param {Boolean} isStorePickup Determines if the stock check is for store pickup.
         */
        getStockAvailability: function (isStorePickup) {
            this.isStorePickup(isStorePickup);
            this.fetchStockAvailability();
            this.openModal();
        },

        /**
         * Opens a modal window associated with delivery options.
         */
        openModal: function () {
            $('#modal_delivery').modal('openModal');
        },

        /**
         * Processes data received from the API and updates observable arrays.
         *
         * @param {Array} response The data received from the API.
         */
        _processData: function(response) {
            const groupedByProductSize = this._groupProductsBySize(response);
            const filteredByStorePickup = this._filterByStorePickup(groupedByProductSize);
            const filteredByHomeDelivery = this._filterByHomeDelivery(groupedByProductSize);
            this.sizesForPickup(filteredByStorePickup);
            this.sizesForDelivery(filteredByHomeDelivery);
            this.stockInfo(groupedByProductSize);
        },

        _groupProductsBySize: function(products) {
            return products.reduce((acc, product) => {
                const size = product.product_size;
                acc[size] = acc[size] || [];
                acc[size].push(product);
                return acc;
            }, {});
        },

        /**
         * Filters grouped products by store pickup availability.
         *
         * @param {Object} groupedProducts Object containing products grouped by their sizes.
         * @return {Object} A filtered object containing only products available for store pickup.
         */
        _filterByStorePickup: function(groupedProducts) {
            return Object.entries(groupedProducts).reduce((acc, [size, products]) => {
                const availableForPickup = products.filter(product =>
                    product.stock.some(stockItem => stockItem.is_store_pickup)
                );
                if (availableForPickup.length) {
                    acc[size] = availableForPickup;
                }
                return acc;
            }, {});
        },

        /**
         * Filters grouped products by home delivery availability.
         * 
         * @param {Object} groupedProducts Object containing products grouped by their sizes.
         * @return {Object} A filtered object containing only products available for home delivery.
         */
        _filterByHomeDelivery: function(groupedProducts) {
            return Object.entries(groupedProducts).reduce((acc, [size, products]) => {
                const availableForHomeDelivery = products.filter(product =>
                    product.stock.some(stockItem => !stockItem.is_store_pickup)
                );
                if (availableForHomeDelivery.length) {
                    acc[size] = availableForHomeDelivery;
                }
                return acc;
            }, {});
        },

        /**
         * Fetches stock availability from the server.
         */
        fetchStockAvailability: function () {
            if (_.isEmpty(this.stockInfo())) {
                const url = urlBuilder.build(`rest/V1/stock-availability/${this.productId}`);

                this.isLoading(true);
                storage.get(url)
                .done((response) => {
                    this._processData(response);
                })
                .fail(() => {
                    console.error('Failed to fetch stock availability');
                })
                .always(() => {
                    this.isLoading(false);
                });
            }
        }
    });
});
