@icon__delivery: '@{baseDir}Omnipro_StockAvailability/images/icon__delivery.svg';
@icon__pickup: '@{baseDir}Omnipro_StockAvailability/images/icon__store-pickup.svg';
//
//  Common (Both desktop and mobile)
//  _____________________________________________
& when (@media-common = true) {
    .modal-storepickup {
        .modal__content {
            .options-availability {
                width: var(--modal-storepickup-options__width, 100%);

                &.header {
                    .lib-icon-font(
                        @icon-down,
                        @_icon-font-size: 30px,
                        @_icon-font-line-height: 12px,
                        @_icon-font-text-hide: false,
                        @_icon-font-margin: 0,
                        @_icon-font-position: after,
                        @_icon-font-display: flex
                    );

                    border-top: var(--modal-storepickup-trigger__border-top, @border-width__base solid @border-color__base);
                    justify-content: var(--modal-storepickup-trigger__justify-content, space-between);
                    align-items: var(--modal-storepickup-trigger__align-items, center);
                    cursor: var(--modal-storepickup-trigger__cursor, pointer);
                    padding: var(--modal-storepickup-trigger__padding, 10px 0);
                    font-size: var(--modal-storepickup-trigger__font-size, 16px);
                    font-weight: var(--modal-storepickup-trigger__font-weight, bold);
                    text-transform: var(--modal-storepickup-trigger__text-transform, uppercase);

                    &:first-of-type {
                        border: var(--modal-storepickup-trigger-first__border, 0);
                    }

                    &.active {
                        .lib-icon-font-symbol(
                            @_icon-font-content: @icon-up,
                            @_icon-font-position: after
                        );
                    }

                    &.active,
                    &:hover {
                        background-color: var(--modal-storepickup-trigger-active-hover__background-color, #ccc);
                    }
                }

                &.content {
                    background-color: var(--modal-storepickup-option-content__background-color, #f9f9f9);
                }

                &__list {
                    margin: var(--modal-storepickup-option-list__margin, 0);
                }

                &__span {
                    margin: var(--modal-storepickup-option-span__margin, 0);
                    padding: var(--modal-storepickup-option-span__padding, 10px 20px);
                    display: block;
                }

                &__item {
                    margin: var(--modal-storepickup-item__margin, 0);
                    padding: var(--modal-storepickup-item__padding, 15px 20px);
                }

                &.delivery {
                    padding: 5px 0;
                }

                &__size-title {
                    font-size: var(--modal-storepickup-size-title__font-size, 16px);
                    font-weight: var(--modal-storepickup-size-title__font-weight, bold);
                }

                &__header {
                    padding: var(--modal-storepickup-availability-header__font-weight, 0 10px);
                }
            }
        }

        .modal-title {
            font-size: var(--modal-storepickup-title__width, 2.4rem) !important; //Override vendor Mercadopago styles
            font-weight: var(--modal-storepickup-title__font-weight, bold);
            padding-bottom: var(--modal-storepickup-title__padding-bottom, 20px);
        }

        .modal-header {
            padding: var(--modal-storepickup-header__padding, 20px 15px) !important;
        }

        .modal-title {
            .lib-icon-image(
                @_icon-image: @icon__delivery,
                @_icon-image-width: 40px,
                @_icon-image-height: 40px,
                @_icon-image-position: before,
                @_icon-image-position-x: 0,
                @_icon-image-position-y: 0,
                @_icon-image-text-hide: false
            );

            display: flex;
            align-items: center;
            column-gap: 10px;

            &::before {
                background-size: var(--modal-storepickup-title-icon__background-size, 40px);
                flex-shrink: 0;
            }
        }

        &:has(.pickup) {
            .modal-title::before {
                background-image: url(@icon__pickup);
            }
        }
    }
}

//  _____________________________________________
//  Desktop +
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .modal-storepickup {
        .modal-inner-wrap {
            width: var(--modal-storepickup-inner-desktop__width, 50%) !important; //Override vendor Mercadopago styles
            padding: var(--modal-storepickup-inner-desktop__padding, 0);
            max-width: var(--modal-storepickup-inner-desktop__max-width, 600px);
        }

        .modal__content {
            align-items: var(--modal-storepickup-content-desktop__align-items, center);
            max-height: var(--modal-storepickup-content-desktop__max-height, 600px);
            padding-bottom: var(--modal-storepickup-content-desktop__padding-bottom, 20px);
            overflow: var(--modal-storepickup-content-desktop__overflow, auto);
        }
    }
}
