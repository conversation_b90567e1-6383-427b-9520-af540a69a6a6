<div data-bind="blockLoader: isLoading">
    <!-- ko if: isStorePickup -->
        <!-- ko ifnot: _.isEmpty(sizesForPickup()) -->
            <div class="modal__content pickup" data-bind="foreach: { data: Object.keys(sizesForPickup()).sort(), afterRender: applyCollapsible }">
                <div class="options-availability header">
                    <span class="options-availability__header" data-bind="text: $t('Size ') + $data"></span>
                </div>
                <div class="options-availability content" data-bind="foreach: { data: $parent.stockInfo()[$data], as: 'product' }">
                    <ul class="options-availability__list" data-bind="foreach: product.stock">
                        <li if="is_store_pickup" class="options-availability__item" data-bind="text: $parent.product_color + ' - ' + quantity + ' ' + $t('unit(s) in ') + source_name"></li>
                    </ul>
                </div>
            </div>
        <!-- /ko -->
        <!-- ko if: _.isEmpty(sizesForPickup()) -->
            <div role="alert" class="message notice">
                <span data-bind="text: $t('Your product is currently not available for in-store pickup, check other option.')"></span>
            </div>
        <!-- /ko -->
    <!-- /ko -->
    <!-- ko ifnot: isStorePickup -->
        <!-- ko ifnot: _.isEmpty(sizesForDelivery()) -->
            <div class="modal__content" data-bind="foreach: { data: Object.keys(sizesForDelivery()).sort(), afterRender: applyCollapsible }">
                <div class="options-availability header">
                    <span class="options-availability__header" data-bind="text: $t('Size ') + $data"></span>
                </div>
                <div class="options-availability content" data-bind="foreach: { data: $parent.stockInfo()[$data], as: 'product' }">
                    <span class="options-availability__span" data-bind="text: product.product_color + ' - ' + $t('In stock')"></span>
                </div>
            </div>
        <!-- /ko -->
        <!-- ko if: _.isEmpty(sizesForDelivery()) -->
            <div role="alert" class="message notice">
                <span data-bind="text: $t('Your product is currently not available for home delivery, check other option.')"></span>
            </div>
        <!-- /ko -->
    <!-- /ko -->
</div>
