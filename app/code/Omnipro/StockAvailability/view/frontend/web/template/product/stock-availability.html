<div class="product-storepickup-box">
    <div if="storePickupAvailabilityEnabled" class="product-storepickup-option __storepickup">
        <div class="product-storepickup-text">
            <span class="product-storepickup-title" data-bind="i18n: 'In-store pickup'"></span>
            <span id="btn_modal_storepickup" class="product-storepickup-link" data-bind="i18n: 'Check availability', click: function() { getStockAvailability(true) }"></span>
        </div>
    </div>
    <div if="homeDeliveryAvailabilityEnabled" class="product-storepickup-option __delivery">
        <div class="product-storepickup-text">
            <span class="product-storepickup-title" data-bind="i18n: 'Home delivery'"></span>
            <span id="btn_modal_delivery" class="product-storepickup-link" data-bind="i18n: 'Check availability', click: function() { getStockAvailability(false) }"></span>
        </div>
    </div>
</div>

<div id="modal_delivery" data-bind="mageInit: {
    'Magento_Ui/js/modal/modal':{
        'type': 'popup',
        'title': $t('Stock Availability'),
        'trigger': '[data-trigger=trigger]',
        'responsive': false,
        'buttons': [],
        'modalClass': 'modal-storepickup'
    }}">
    <div class="content">
        <!-- ko template: 'Omnipro_StockAvailability/product/stock-availability-info' --><!-- /ko -->
    </div>
</div>
