<?xml version="1.0"?>
<!--
 * @copyright Copyright (c) 2025 Omnipro
 * <AUTHOR> Team
 * @category Omnipro
 * @module Omnipro/CustomerIdentification
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <omnipro_customeridentification>
            <settings>
                <type_document>RUT (11111111-1),Pasaporte,DNI,RUC,Carnet de extranjería</type_document>
                <rut_validation>1</rut_validation>
            </settings>
        </omnipro_customeridentification>
    </default>
</config>
