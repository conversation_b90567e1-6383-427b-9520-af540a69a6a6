<?xml version="1.0"?>
<!--
 * @copyright Copyright (c) 2025 Omnipro
 * <AUTHOR> Team
 * @category Omnipro
 * @module Omnipro/CustomerIdentification
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Eav\Model\Entity\Attribute\Source\Table">
        <plugin name="omnipro_customeridentification_documenttype_plugin" type="Omnipro\CustomerIdentification\Plugin\Model\Entity\Attribute\Source\DocumentType"/>
    </type>
    <type name="Magento\Customer\Controller\Address\FormPost">
        <plugin name="omnipro_customer_identification_form_post" type="Omnipro\CustomerIdentification\Plugin\Frontend\Controller\FormPost" disabled="false"/>
    </type>
</config>
