<?xml version="1.0"?>
<!--
 * @copyright Copyright (c) 2025 Omnipro
 * <AUTHOR> Team
 * @category Omnipro
 * @module Omnipro/CustomerIdentification
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Checkout\Block\Checkout\LayoutProcessor">
        <plugin name="omnipro_customeridentification_layoutprocessor" sortOrder="10"
                type="Omnipro\CustomerIdentification\Plugin\Block\Checkout\LayoutProcessorPlugin"/>
    </type>
    <type name="Magento\Checkout\Model\CompositeConfigProvider">
        <arguments>
            <argument name="configProviders" xsi:type="array">
                <item name="rut_validate" xsi:type="object">Omnipro\CustomerIdentification\Model\ConfigProvider</item>
            </argument>
        </arguments>
    </type>
</config>
