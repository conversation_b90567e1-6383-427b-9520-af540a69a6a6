<?xml version="1.0"?>
<!--
 * @copyright Copyright (c) 2025 Omnipro
 * <AUTHOR> Team
 * @category Omnipro
 * @module Omnipro/CustomerIdentification
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="omnipro_customeridentification" translate="label" type="text" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Customer Identification</label>
            <tab>omnipro</tab>
            <resource>Omnipro_CustomerIdentification::config</resource>
            <group id="settings" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Settings</label>
                <field id="type_document" translate="label" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Type document Options</label>
                    <source_model>Omnipro\CustomerIdentification\Model\Config\Source\DocumentType</source_model>
                    <comment>Select options available for specific store</comment>
                </field>
                <field id="rut_validation" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable/Disable Rut Validation</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Enable or Disable Rut validation</comment>
                </field>
            </group>
        </section>
    </system>
</config>
