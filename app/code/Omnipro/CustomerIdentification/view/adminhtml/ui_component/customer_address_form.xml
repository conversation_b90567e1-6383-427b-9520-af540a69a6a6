<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd" component="Magento_Customer/js/form/components/form">
    <fieldset name="general">
        <field name="type_document" component="Omnipro_CustomerIdentification/js/form/element/type-document"
               formElement="select" sortOrder="90">
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Type Document</label>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Omnipro\CustomerIdentification\Model\Customer\Attribute\Source\TypeDocument"/>
                        <filterBy>
                            <field>country_id</field>
                            <target>${ $.provider }:${ $.parentScope }.country_id</target>
                        </filterBy>
                    </settings>
                </select>
            </formElements>
        </field>
    </fieldset>
</form>
