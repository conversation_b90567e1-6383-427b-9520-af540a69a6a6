define([
    'underscore',
    'uiRegistry',
    'Magento_Ui/js/form/element/select'
], function (_, registry, Select) {
    'use strict';

    return Select.extend({
        defaults: {
            imports: {
                update: '${ $.parentName }.country_id:value'
            }
        },

        /**
         * Filtra las opciones del select basado en el country_id seleccionado.
         *
         * @param {String} countryValue - Código del país seleccionado (ej: 'US', 'MX').
         */
        filter: function (countryValue) {
            var filteredOptions;

            this._super(countryValue, 'country_id');

            if (!countryValue) {
                this.setOptions(this.initialOptions);
                return;
            }

            filteredOptions = _.filter(this.initialOptions, function (item) {
                return item.country_id && item.country_id.includes(countryValue);
            });

            this.setOptions(filteredOptions);
            this.reset();
        }
    });
});
