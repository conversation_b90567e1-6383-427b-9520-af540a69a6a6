/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/**
 * @api
 */
define([
    'Magento_Ui/js/form/element/abstract',
    'uiRegistry'
], function (Input, registry) {
    'use strict';

    return Input.extend({
        defaults: {
            visible: false,
            imports: {
                country: '${ $.parentName }.country_id:value'
            },
            modules: {
                documentTypeComponent: '${ $.parentName }.type_document',
            },
            listens: {
                '${ $.parentName }.type_document:value': 'handleDocumentTypeChange'
            },
        },

        /**
         * Initialize component
         */
        initialize: function () {
            this._super();
            registry.async('index = type_document')(() => {
                this.handleDocumentTypeChange(this.documentTypeComponent().value());
            });

        },
        /**
         * Toggle visibility based on the document type value
         * @param {string} documentTypeValue
         */
        handleDocumentTypeChange: function (documentTypeValue) {
            let isRUC = this.documentTypeComponent().options().some(option => option.value === documentTypeValue && option.label === 'RUC');
            this.visible(isRUC);
            if (!isRUC) {
                this.value('');
            }
        },
    });
});

