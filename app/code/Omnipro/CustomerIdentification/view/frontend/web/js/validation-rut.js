/**
 * <AUTHOR> Team
 * @copyright   Copyright (c) 2023 OmniPro (https://omni.pro/)
 * @module      Omnipro/CustomerAddress
 */
define([
    'jquery',
    'jquery/validate'
], function (
    $
) {
    "use strict";

    function isValidationRut() {
        return window.checkoutConfig['validate_rut']['is_active'];
    }
    function calculateVerifierDigit(number) {
        let sum = 0;
        let mul = 2;

        let i = number.length;
        while (i--) {
            sum = sum + parseInt(number.charAt(i)) * mul;
            if (mul % 7 === 0) {
                mul = 2;
            } else {
                mul++;
            }
        }

        const res = sum % 11;

        if (res === 0) {
            return '0';
        } else if (res === 1) {
            return 'k';
        }

        return `${11 - res}`;
    }

    return function (validator) {
        const documentLabel = 'RUT (11111111-1)';
        validator.addRule('validate-verifier-digit', function (value) {
            if (!isValidationRut()) {
                return true;
            }

            let typeDocumentSelector;

            if ($('.form-shipping-address:visible').length) {
                typeDocumentSelector = 'div[name="shippingAddress.custom_attributes.type_document"] select';
            } else if ($('.billing-address-form:visible').length) {
                typeDocumentSelector = 'div[name="billingAddressmercadopago_custom.custom_attributes.type_document"] select';
            }

            if ($('.form-pickup-form:visible').length) {
                typeDocumentSelector = 'div[name="storePickup.type_document"] select';
            }

            let typeDocument;
            if (typeDocumentSelector) {
                typeDocument = $(typeDocumentSelector).find(":selected").text();
            }

            let rut = value.replace(/[\.\-]/g, ''),
            body = rut.slice(0, -1),
            checkDigit = rut.slice(-1).toLowerCase();

            if (typeDocument !== documentLabel) {
                return true;
            }
            const rutPattern = /^\d{7,8}-[0-9kK]$/;
            if (!rutPattern.test(value)) {
                return false;
            }

            return checkDigit === calculateVerifierDigit(body);

        }, $.mage.__("Please enter a valid RUT..."));

        return validator;
    };
});
