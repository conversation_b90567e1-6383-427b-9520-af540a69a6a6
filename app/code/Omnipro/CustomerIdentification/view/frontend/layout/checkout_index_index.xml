<?xml version="1.0"?>
<!--
 * @copyright Copyright (c) 2025 Omnipro
 * <AUTHOR> Team
 * @category Omnipro
 * @module Omnipro/CustomerIdentification
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="content">
            <referenceBlock name="checkout.root">
                <arguments>
                    <argument name="jsLayout" xsi:type="array">
                        <item name="components" xsi:type="array">
                            <item name="checkout" xsi:type="array">
                                <item name="children" xsi:type="array">
                                    <item name="steps" xsi:type="array">
                                        <item name="children" xsi:type="array">
                                            <item name="shipping-step" xsi:type="array">
                                                <item name="children" xsi:type="array">
                                                    <item name="shippingAddress" xsi:type="array">
                                                        <item name="children" xsi:type="array">
                                                            <item name="shipping-address-fieldset" xsi:type="array">
                                                                <item name="displayArea" xsi:type="string">additional-fieldsets</item>
                                                                <item name="children" xsi:type="array">
                                                                    <!-- The following items override configuration of corresponding address attributes -->
                                                                    <item name="region" xsi:type="array">
                                                                        <!-- Make region attribute invisible on frontend. Corresponding input element is created by region_id field -->
                                                                        <item name="visible" xsi:type="boolean">false</item>
                                                                    </item>
                                                                    <item name="street" xsi:type="array">
                                                                        <item name="sortOrder" xsi:type="string">70</item>
                                                                    </item>
                                                                    <item name="number" xsi:type="array">
                                                                        <item name="sortOrder" xsi:type="string">71</item>
                                                                    </item>
                                                                    <item name="department" xsi:type="array">
                                                                        <item name="sortOrder" xsi:type="string">72</item>
                                                                    </item>
                                                                    <item name="type_document" xsi:type="array">
                                                                        <item name="sortOrder" xsi:type="string">100</item>
                                                                    </item>
                                                                    <item name="document" xsi:type="array">
                                                                        <item name="sortOrder" xsi:type="string">101</item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </argument>
                </arguments>
            </referenceBlock>
        </referenceContainer>
    </body>
</page>
