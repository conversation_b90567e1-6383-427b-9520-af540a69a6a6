<?php

namespace Omnipro\CustomerIdentification\Setup\Patch\Data;

use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchVersionInterface;
use Omnipro\CustomerIdentification\Model\Attribute\Backend\Rut;

class UpdateDocumentAttributeBackend implements DataPatchInterface, PatchVersionInterface
{
    /**
     * @param CustomerSetupFactory $customerSetupFactory
     */
    public function __construct(
        private CustomerSetupFactory $customerSetupFactory
    ) {
    }

    /**
     * Get an array of patches that have to be executed beforehand
     *
     * @return string[]
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * Get aliases (previous names) for the patch
     *
     * @return string[]
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * Define the patch version
     *
     * @return string
     */
    public static function getVersion()
    {
        return '1.0.1';
    }

    /**
     * Run code inside patch
     *
     * @return void
     */
    public function apply()
    {
        $customerSetup = $this->customerSetupFactory->create();

        $customerSetup->updateAttribute(
            'customer_address',
            'document',
            'backend_model',
            Rut::class
        );
    }
}
