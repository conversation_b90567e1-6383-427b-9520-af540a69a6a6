<?php

/**
 *  Copyright (c) 2025 - Omnipro (https://www.omni.pro/)
 * *  <AUTHOR> Team
 * *  @category Omnipro
 * *  @module Omnipro/CustomerIdentification
 */
namespace Omnipro\CustomerIdentification\Setup\Patch\Data;

use Magento\Customer\Model\Indexer\Address\AttributeProvider;
use Magento\Customer\Setup\CustomerSetup;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Eav\Model\Config;
use Magento\Eav\Model\Entity\Attribute\SetFactory as AttributeSetFactory;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Validator\ValidateException;

/**
 * Datapatch for add new attribute
 */
class AddRazonSocialAttribute implements DataPatchInterface
{
    /**
     * @var string
     */
    public const ATTRIBUTE_CODE = 'razon_social';

    /**
     * AddRazonSocialAttribute constructor.
     * @param CustomerSetupFactory $customerSetupFactory
     * @param AttributeSetFactory $attributeSetFactory
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param EavSetupFactory $eavSetupFactory
     * @param Config $eavConfig
     */
    public function __construct(
        private CustomerSetupFactory $customerSetupFactory,
        private AttributeSetFactory $attributeSetFactory,
        private ModuleDataSetupInterface $moduleDataSetup,
        private EavSetupFactory $eavSetupFactory,
        private Config $eavConfig
    ) {
    }

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     * @throws LocalizedException
     * @throws ValidateException
     * @throws \Exception
     */
    public function apply()
    {
        if (!$this->isAttributeExists()) {
            $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
            $eavSetup->addAttribute('customer_address', self::ATTRIBUTE_CODE, [
                'type' => 'varchar',
                'label' => 'Razón social',
                'input' => 'text',
                'required' => false,
                'visible' => true,
                'sort_order' => 106,
                'user_defined' => true,
                'system' => false,
                'default_value' => '',
                'position' => 106,
                'group' => 'General',
            ]);
            $customAttribute = $this->eavConfig->getAttribute('customer_address', self::ATTRIBUTE_CODE);
            $customAttribute->setData('used_in_forms', [
                'adminhtml_customer_address',
                'customer_address_edit',
                'customer_register_address'
            ]);
            $customAttribute->save();
            $this->moduleDataSetup->getConnection()->endSetup();
        }
    }

    /**
     * {@inheritdoc}
     */
    public function revert()
    {
        $this->moduleDataSetup->getConnection()->startSetup();
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->customerSetupFactory->create(['setup' => $this->moduleDataSetup]);
        $customerSetup->removeAttribute(AttributeProvider::ENTITY, self::ATTRIBUTE_CODE);

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    /**
     * {@inheritdoc}
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * {@inheritdoc}
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * Check if a specified attribute exists.
     *
     * @return bool Returns true if the attribute exists, false otherwise.
     * @throws LocalizedException
     */
    protected function isAttributeExists(): bool
    {
        $attribute = $this->eavConfig->getAttribute(AttributeProvider::ENTITY, self::ATTRIBUTE_CODE);
        if ($attribute && $attribute->getId()) {
            return true;
        }
        return false;
    }
}
