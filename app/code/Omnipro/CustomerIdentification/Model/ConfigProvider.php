<?php

/**
 * <AUTHOR> Team
 * @package  Omnipro\CustomerIdentification\Model
 * @category Omnipro
 * @copyright Copyright (c) 2025 Omnipro
 */

namespace Omnipro\CustomerIdentification\Model;

use Magento\Checkout\Model\ConfigProviderInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Class ConfigProvider
 */
class ConfigProvider implements ConfigProviderInterface
{
    /**
     * @param Config $config
     */
    public function __construct(
        private Config $config
    ) {
    }

    /**
     * @return array[]
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function getConfig()
    {
        return [
            'validate_rut' => [
                'is_active' => $this->config->isValidationActive()
            ],
        ];
    }
}
