<?php

/**
 * <AUTHOR> Team
 * @package  Omnipro\CustomerIdentification\Model
 * @category Omnipro
 * @copyright Copyright (c) 2025 Omnipro
 */

namespace Omnipro\CustomerIdentification\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Class Config
 */
class Config
{
    /**
     * Config paths constants district code for sorting
     */
    private const XML_PATH_URL_DOCUMENT_TYPE_OPTIONS = 'omnipro_customeridentification/settings/type_document';

    /**
     * Config paths to get rut validation status
     */
    private const XML_PATH_URL_RUT_VALIDATION= 'omnipro_customeridentification/settings/rut_validation';

    /**
     * @param ScopeConfigInterface $scopeConfig
     * @param StoreManagerInterface $storeManager
     * @param SerializerInterface $serializer
     */
    public function __construct(
        private ScopeConfigInterface $scopeConfig,
        private StoreManagerInterface $storeManager,
        private SerializerInterface $serializer
    ) {
    }

    /**
     * Get Document Type Options
     *
     * @param null $storeCode
     * @return array
     * @throws NoSuchEntityException
     */
    public function getDocumentTypeOptions($storeCode = null): array
    {
        $documentTypeOptions = $this->scopeConfig->getValue(
            self::XML_PATH_URL_DOCUMENT_TYPE_OPTIONS,
            ScopeInterface::SCOPE_STORE,
            $storeCode ?: $this->storeManager->getStore()->getCode()
        ) ?: '';

        return explode(',', $documentTypeOptions);
    }

    /**
     * @param string $path
     * @param string|null $storeCode
     * @param string $scope
     * @return mixed
     * @throws NoSuchEntityException
     */
    public function getConfigValue(string $path, ?string $storeCode = null, string $scope = 'store'): mixed
    {
        return $this->scopeConfig->getValue($path, $scope, $storeCode ?: $this->storeManager->getStore()->getCode());
    }

    /**
     * Get is validation active by website code
     *
     * @param null $websiteCode
     * @return bool
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function isValidationActive($websiteCode = null): bool
    {
        if ($websiteCode) {
            return $this->getConfigValue(self::XML_PATH_URL_RUT_VALIDATION, $websiteCode, 'website');
        }
        $websiteCode = $this->getCurrentWebsiteCode();
        return $this->getConfigValue(
            self::XML_PATH_URL_RUT_VALIDATION,
            $websiteCode,
            $websiteCode ? 'website' : 'store'
        ) && $this->checkCountry();
    }

    /**
     * @return bool
     * @throws NoSuchEntityException
     */
    private function checkCountry(): bool
    {
        return $this->getConfigValue('general/country/default') == 'CL';
    }

    /**
     * Get current website code
     *
     * @return string
     * @throws LocalizedException
     */
    public function getCurrentWebsiteCode()
    {
        return $this->storeManager->getWebsite()->getCode();
    }
}
