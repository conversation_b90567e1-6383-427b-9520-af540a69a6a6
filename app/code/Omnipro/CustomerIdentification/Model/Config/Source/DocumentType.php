<?php

/*
 *  Copyright (c) 2025 - Omnipro (https://www.omni.pro/)
 *  <AUTHOR> Team
 *  @category Omnipro
 *  @module Omnipro/CustomerAddress
 */

namespace Omnipro\CustomerIdentification\Model\Config\Source;

use <PERSON>gento\Customer\Api\AddressMetadataInterface;
use Magento\Eav\Model\Config;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Class DocumentType
 */
class DocumentType implements OptionSourceInterface
{
    public const ATTRIBUTE_CODE = 'type_document';

    /**
     * @var array
     */
    protected $options;

    /**
     * @param Config $eavConfig
     */
    public function __construct(
        private Config $eavConfig
    ) {
    }

    /**
     * @return array
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function toOptionArray()
    {
        if (!$this->options) {
            $this->options = [];
            $options = $this->getDocumentTypeOptions();
            if ($options) {
                foreach ($options as $option) {
                    $this->options[] = [
                        'value' => $option['value'],
                        'label' => $option['label']
                    ];
                }
            }
        }
        return $this->options;
    }

    /**
     * Get Document Type Options
     *
     * @return array
     * @throws LocalizedException
     */
    protected function getDocumentTypeOptions()
    {
        try {
            $attribute = $this->eavConfig->getAttribute(
                AddressMetadataInterface::ENTITY_TYPE_ADDRESS,
                self::ATTRIBUTE_CODE
            );
            $options = $attribute->getSource()->getAllOptions();
            $optionsArray = [];
            foreach ($options as $option) {
                $optionsArray[] = [
                    'value' => $option['label'],
                    'label' => $option['label']
                ];
            }
            return $optionsArray;
        } catch (NoSuchEntityException $e) {
            return [];
        }
    }
}
