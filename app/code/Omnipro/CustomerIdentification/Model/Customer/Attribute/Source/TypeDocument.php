<?php
/**
 * Created on Feb 07 2025
 * <AUTHOR> Team
 * @package  Omnipro\CustomerIdentification\Model\Customer\Attribute\Source
 * @category Omnipro
 * @copyright Copyright (c) 2025 Omnipro
 */

namespace Omnipro\CustomerIdentification\Model\Customer\Attribute\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Omnipro\CustomerIdentification\Model\Config;

/**
 * Class TypeDocument
 */
class TypeDocument extends AbstractSource
{
    /**
     * Constructor.
     *
     * @param StoreManagerInterface $storeManager
     * @param Config $config
     */
    public function __construct(
        private StoreManagerInterface $storeManager,
        private Config $config
    ) {
    }

    /**
     * Obtiene las opciones basadas en la configuración de cada store.
     *
     * @return array
     * @throws NoSuchEntityException
     */
    public function getAllOptions()
    {
        $options = [
            [
                'value' => '',
                'label' => __('Select an option'),
                'country_id' => []
            ]
        ];
        $uniqueValues = [];
        $stores = $this->storeManager->getStores();

        foreach ($stores as $store) {
            $storeCode = $store->getCode();
            $selectedValues = $this->config->getDocumentTypeOptions($storeCode);
            $countryId = $this->getCountryByStore($storeCode);

            foreach ($selectedValues as $documentType) {
                $documentType = trim($documentType);

                if ($documentType === '') {
                    continue;
                }
                $key = $documentType . '_' . $countryId;

                if (!isset($uniqueValues[$key])) {
                    $options[] = [
                        'value' => $documentType,
                        'label' => __(ucfirst($documentType)),
                        'country_id' => [$countryId]
                    ];
                    $uniqueValues[$key] = true;
                }
            }
        }

        return $options;
    }

    /**
     * Obtiene el código de país asociado a una store.
     *
     * @param string $storeCode
     * @return string|null
     * @throws NoSuchEntityException
     */
    protected function getCountryByStore($storeCode)
    {
        return $this->config->getConfigValue(
            'general/country/default',
            $storeCode,
            ScopeInterface::SCOPE_STORE
        );
    }
}
