<?php

namespace Omnipro\CustomerIdentification\Model\Attribute\Backend;

use Magento\Eav\Model\Config;
use Magento\Eav\Model\Entity\Attribute\Backend\AbstractBackend;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\LocalizedException;

class Rut extends AbstractBackend
{
    protected const CODE_TYPE_DOCUMENT = 'type_document';

    /**
     * @param Config $eavConfig
     */
    public function __construct(
        private Config $eavConfig
    ) {
    }

    /**
     * After attribute load.
     *
     * @param DataObject $object
     * @return $this
     */
    public function afterLoad($object)
    {
        return parent::afterLoad($object);
    }

    /**
     * Before attribute save.
     *
     * @param DataObject $object
     * @return $this
     * @throws LocalizedException
     */
    public function beforeSave($object)
    {
        $this->validaRut($object);
        return parent::beforeSave($object);
    }
    /**
     * Validate RUT value.
     *
     * @param DataObject $object
     * @return void
     * @throws LocalizedException
     */
    private function validaRut($object)
    {

        $attributeCode  = $this->getAttribute()->getAttributeCode();
        $typeDocumentLabel = $this->getAttributeText(
            self::CODE_TYPE_DOCUMENT,
            $object->getData(self::CODE_TYPE_DOCUMENT)
        );

        if (empty($typeDocumentLabel) || strpos($typeDocumentLabel, 'RUT') === false) {
            return;
        }

        $value          = $object->getData($attributeCode);
        $rut            = trim($value);

        if ($rut == "") {
            throw new LocalizedException(
                __('RUT cannot be empty')
            );
        }

        if (!preg_match("/^[0-9.]+[-]{1}+[0-9kK]{1}/", $value)) {
            throw new LocalizedException(
                __('The RUT "%1" is invalid', $rut)
            );
        }
        $rut = preg_replace('/[\.\-]/i', '', $rut);
        $dv = substr($rut, -1);
        $numero = substr($rut, 0, strlen($rut) - 1);
        $i = 2;
        $suma = 0;
        foreach (array_reverse(str_split($numero)) as $v) {
            if ($i == 8) {
                $i = 2;
            }
            $suma += $v * $i;
            ++$i;
        }
        $dvr = 11 - ($suma % 11);
        if ($dvr == 11) {
            $dvr = 0;
        }
        if ($dvr == 10) {
            $dvr = 'K';
        }
        if ($dvr != strtoupper($dv)) {
            throw new LocalizedException(
                __('The RUT "%1" is invalid', $value)
            );
        }
    }

    /**
     * @param $attributeCode
     * @param $optionId
     * @return mixed|null
     * @throws LocalizedException
     */
    private function getAttributeText($attributeCode, $optionId)
    {
        $attribute = $this->getAttribute($attributeCode);
        $attribute = $this->eavConfig->getAttribute('customer_address', $attributeCode);
        if ($attribute) {
            $options = $attribute->getSource()->getAllOptions();
            foreach ($options as $option) {
                if ($option['value'] == $optionId) {
                    return $option['label'];
                }
            }
        }
        return null;
    }
}
