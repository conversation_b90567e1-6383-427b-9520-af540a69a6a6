<?php
namespace Omnipro\SecondImageProdHover\Helper;

use Magento\Catalog\Model\ProductRepository;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Catalog\Model\Product\Gallery\ReadHandler as GalleryReadHandler;

class SecondImage extends AbstractHelper
{
    /**
     * @var ProductRepository The repository for product data
    */
    protected $productRepository;
    /**
     * @var GalleryReadHandler Handler to load the media gallery images
    */
    protected $galleryReadHandler;

    /**
     * Constructor for SecondImage helper.
     *
     * @param \Magento\Framework\App\Helper\Context $context
     * @param ProductRepository $productRepository Repository to access product data
     * @param GalleryReadHandler $galleryReadHandler Handler to ensure media gallery is loaded
     * @param array $data Optional data array
    */

    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        ProductRepository $productRepository,
        GalleryReadHandler $galleryReadHandler,
        array $data = []
    ) {
        $this->productRepository = $productRepository;
        $this->galleryReadHandler = $galleryReadHandler;
        parent::__construct($context, $data);
    }

    /**
     * Retrieves the URL of the second image in the product's media gallery.
     *
     * This method will load the product by ID, ensure its media gallery is loaded,
     * and attempt to retrieve the URL of the second image. If the product has fewer than
     * two images or an error occurs, it will return false.
     *
     * @param int $productId The ID of the product to retrieve the second image for.
     * @return string|false URL of the second image or false if not available.
    */

    public function getSecondImageUrl($productId)
    {
        // Load the product by ID
        $product = $this->productRepository->getById($productId);

        // Ensure the gallery is loaded
        $this->galleryReadHandler->execute($product);

        $images = $product->getMediaGalleryImages();
        if ($images && $images->getSize() > 1) {
            $items = array_values($images->getItems());
            $secondImage = $items[1]; // Get the second image
            return $secondImage->getUrl();
        }
        return false; // Return false if no second image exists
    }
}
