# [1.0.0] - OmniPro_SecondImageProdHover Module

### Author
<PERSON><PERSON><PERSON> <<EMAIL>> || OmniPro Team

### Package
OmniPro_SecondImageProdHover

## Associated tickets
- https://omnipro.atlassian.net/browse/SLAKRO-20

## OmniPro_SecondImageProdHover module by Omni.pro

- [Main Functionalities](#markdown-header-main-functionalities)
- [Installation](#markdown-header-installation)
- [Specifications](#markdown-header-specifications)

## Main Functionalities
- Add the functionality of adding a second images to product card

## Installation
install by running `php bin/magento setup:upgrade`\*

### Zip file

- Unzip the zip file in `app/code/Omnipro`
- Apply database updates by running `php bin/magento setup:upgrade`\*
- Flush the cache by running `php bin/magento cache:flush`

## Specifications
-

### Copyright
Copyright (c) 2024 OmniPro (https://omni.pro/)
