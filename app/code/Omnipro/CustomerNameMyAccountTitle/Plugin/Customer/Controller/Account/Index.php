<?php
namespace Omnipro\CustomerNameMyAccountTitle\Plugin\Customer\Controller\Account;

class Index
{
    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $customerSession;

    /**
     * @param \Magento\Customer\Model\Session $sessionSession
     */
    public function __construct(
        \Magento\Customer\Model\Session $sessionSession
    ) {
        $this->customerSession = $sessionSession;
    }

    public function afterExecute(
        \Magento\Customer\Controller\Account\Index $subject,
        $resultPage
    ) {
        $resultPage->getConfig()->getTitle()->set(__('We welcome you back, %1', $this->customerSession->getCustomer()->getName()));

        return $resultPage;
    }
}
