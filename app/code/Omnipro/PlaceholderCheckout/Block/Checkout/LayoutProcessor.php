<?php
/**
 * Copyright © 2024 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> Team
 * @date        23/04/24, 19:00
 * @category    Omnipro
 * @module      Omnipro/PlaceholderCheckout
 */

namespace Omnipro\PlaceholderCheckout\Block\Checkout;

use Magento\Checkout\Block\Checkout\LayoutProcessorInterface;
use Omnipro\PlaceholderCheckout\Api\ConfigInterface as scopeConfig;

/**
 * Layout processor for checkout with customer address search.
 */
class LayoutProcessor implements LayoutProcessorInterface
{
    /**
     * Const for custom attribute codes
     * @var string
     */
    private const CUSTOM_DOCUMENT = 'document';
    private const CUSTOM_NUMBER = 'number';
    private const CUSTOM_RAZON_SOCIAL = 'razon_social';
    private const CUSTOM_DEPARTAMENT = 'department';

    /**
     * @param scopeConfig $scopeConfig
     */
    public function __construct(
        private readonly scopeConfig $scopeConfig
    ) {
    }

    /**
     * Process js Layout of block
     *
     * @param array $jsLayout
     * @return array
     */
    public function process($jsLayout)
    {
        if ($this->scopeConfig->getIsPlaceholderActive()) {
            $this->addPlaceholder($jsLayout);
        }

        return $jsLayout;
    }

    /**
     * Returns a list of custom attributes for customer addresses.
     *
     * @param array $jsLayout
     * @return void
     */
    private function addPlaceholder(array &$jsLayout): void 
    {
        if (isset($jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']
            ['children']['shipping-address-fieldset']['children'][self::CUSTOM_DOCUMENT])) {
            $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']
            ['children']['shipping-address-fieldset']['children'][self::CUSTOM_DOCUMENT]['placeholder'] = __('Enter the number');
        }

        if (isset($jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']
            ['children']['shipping-address-fieldset']['children'][self::CUSTOM_NUMBER])) {
            $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']
            ['children']['shipping-address-fieldset']['children'][self::CUSTOM_NUMBER]['placeholder'] = __('Enter the number');
        }

        if (isset($jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']
            ['children']['shipping-address-fieldset']['children'][self::CUSTOM_RAZON_SOCIAL])) {
            $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']
            ['children']['shipping-address-fieldset']['children'][self::CUSTOM_RAZON_SOCIAL]['placeholder'] = __('Enter the company name');
        }

        if (isset($jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']
            ['children']['shipping-address-fieldset']['children'][self::CUSTOM_DEPARTAMENT])) {
            $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']
            ['children']['shipping-address-fieldset']['children'][self::CUSTOM_DEPARTAMENT]['placeholder'] = __('Enter the apartment number, office, etc…');
        }
    }
}
