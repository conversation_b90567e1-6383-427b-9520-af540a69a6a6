<?php
/**
 * Copyright © 2024 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> Team
 * @date        23/04/24, 19:00
 * @category    Omnipro
 * @module      Omnipro/PlaceholderCheckout
 */

declare(strict_types=1);

namespace Omnipro\PlaceholderCheckout\Plugin\Frontend\Block\Checkout;

use Magento\Checkout\Block\Checkout\LayoutProcessor as MagentoLayoutProcessor;
use Omnipro\PlaceholderCheckout\Api\ConfigInterface as scopeConfig;

/**
 * Layout Processor plugin class
 * @class Omnipro\PlaceholderCheckout\Plugin\Frontend\Block\Checkout\LayoutProcessor
 */
class LayoutProcessor
{
    /**
     * @param scopeConfig $scopeConfig
     */
    public function __construct(
        private readonly scopeConfig $scopeConfig
    ) {
    }

    /**
     * Checkout LayoutProcessor after process plugin.
     * Adds the placeholder text to the Street fields of Shipping and Billing addresses form.
     *
     * @param MagentoLayoutProcessor $subject
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess(MagentoLayoutProcessor $subject, array $jsLayout): array
    {
        if($this->scopeConfig->getIsPlaceholderActive()) {
            $components = &$jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']
            ['shippingAddress']['children']['shipping-address-fieldset']['children'];

            $placeholders = [
                'firstname' => __('Enter your name'),
                'lastname' => __('Enter your last name'),
                'telephone' => __('Enter your phone'),
            ];

            foreach($components as $name => &$component) {
                if (isset($placeholders[$name])) {
                    $component['placeholder'] = $placeholders[$name];
                } else {
                    $component['placeholder'] = __('Enter a value'); // Default placeholder
                }
            }

            if (isset($components['street']['children'][0])) {
                $components['street']['children'][0]['placeholder'] = __('Enter the street');
            } 
        }

        return $jsLayout;
    }
}
