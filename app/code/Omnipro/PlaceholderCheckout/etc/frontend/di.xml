<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Checkout\Block\Checkout\LayoutProcessor">
        <plugin name="placehoder_checkout_layout_processor" type="Omnipro\PlaceholderCheckout\Plugin\Frontend\Block\Checkout\LayoutProcessor" sortOrder="300" disabled="false" />
    </type>
    <!-- Custom Layout Processor -->
    <type name="Magento\Checkout\Block\Onepage">
        <arguments>
            <argument name="layoutProcessors" xsi:type="array">
                <item name="omnipro_placeholder_checkout" xsi:type="object" sortOrder="60" >Omnipro\PlaceholderCheckout\Block\Checkout\LayoutProcessor</item>
            </argument>
        </arguments>
    </type>
</config>
