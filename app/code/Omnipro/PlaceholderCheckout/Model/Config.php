<?php
/**
 * Copyright (c) 2024. Omnipro (https://www.omni.pro/).
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\PlaceholderCheckout\Model
 * @category Omnipro
 */

declare(strict_types = 1);

namespace Omnipro\PlaceholderCheckout\Model;

use Magento\Store\Model\ScopeInterface;
use Omnipro\PlaceholderCheckout\Api\ConfigInterface;
use Magento\Framework\App\Config\ScopeConfigInterface as scopeConfig;

/**
 * Config Model Implementation
 */
class Config implements ConfigInterface
{
    /**
     * Discount Percent Active
     * @var string
     */
    private const PLACEHOLDER_CHECKOUT = 'omnipro_placeholder_checkout/placeholdercheckout/enabled';

    /**
     * @param scopeConfig $scopeConfig
     */
    public function __construct(
        private readonly scopeConfig $scopeConfig
    ) {
    }

    /**
     * @inheritDoc
     */
    public function getIsPlaceholderActive(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::PLACEHOLDER_CHECKOUT,
            ScopeInterface::SCOPE_WEBSITES
        );
    }
}
