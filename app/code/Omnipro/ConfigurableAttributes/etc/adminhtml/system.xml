<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="omnipro_configurable_attributes" translate="label"
                 sortOrder="200" showInDefault="1" showInWebsite="1" showInStore="1">
            <class>separator-top</class>
            <label>Omnipro Configurable Attributes</label>
            <tab>omnipro</tab>
            <resource>Omnipro_ConfigurableAttributes::configurable_attributes</resource>
            <group id="general" translate="label" type="text"
                   sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Configuration</label>
                <field id="enabled" translate="label" type="select"
                       sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Module Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
