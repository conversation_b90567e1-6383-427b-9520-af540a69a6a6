<?php
/**
 *
 * @package Omnipro_ConfigurableAttributes
 * <AUTHOR> <<EMAIL>>
 */

namespace Omnipro\ConfigurableAttributes\Setup\Patch\Data;

use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Validator\ValidateException;
use Omnipro\ConfigurableAttributes\Model\Attribute\Source\PrincipalColor;
use Psr\Log\LoggerInterface;
/**
 * Class patch
 */
class PrincipalColorAttributePatchData implements DataPatchInterface
{
    /**
     * public const
     */
    public const PRINCIPAL_COLOR = 'principal_color';

    /**
     * @param ModuleDataSetupInterface $moduleDataSetup
     * @param EavSetupFactory $eavSetupFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        protected ModuleDataSetupInterface $moduleDataSetup,
        protected EavSetupFactory $eavSetupFactory,
        protected LoggerInterface $logger
    ) {
    }

    /**
     * Apply
     *
     * @return void
     */
    public function apply(): void
    {
        try {
            $eavSetup = $this->eavSetupFactory->create(['setup' => $this->moduleDataSetup]);
            $eavSetup->addAttribute(Product::ENTITY, self::PRINCIPAL_COLOR, [
                'type' => 'int',
                'backend' => '',
                'frontend' => '',
                'label' => 'Color Principal',
                'input' => 'select',
                'class' => '',
                'source' => PrincipalColor::class,
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'visible' => true,
                'required' => false,
                'user_defined' => false,
                'default' => '0',
                'searchable' => false,
                'filterable' => false,
                'comparable' => false,
                'visible_on_front' => true,
                'used_in_product_listing' => true,
                'unique' => false
            ]);
        } catch (\Exception $e) {
            $this->logger->critical($e);
        }

        $attribute = $eavSetup->getAttribute(Product::ENTITY, self::PRINCIPAL_COLOR);

        // Asignamos el atributo al conjunto de atributos por defecto
        $eavSetup->addAttributeToSet(
            Product::ENTITY,
            'Default',
            null,
            $attribute['attribute_id']
        );

        $this->moduleDataSetup->endSetup();
    }

    /**
     * Get dependencies
     *
     * @return array|string[]
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * Get aliases
     *
     * @return array|string[]
     */
    public function getAliases()
    {
        return [];
    }
}
