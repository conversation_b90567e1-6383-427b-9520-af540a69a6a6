<?php
/**
 *
 * @package Omnipro_ConfigurableAttributes
 * <AUTHOR> <<EMAIL>>
 */

namespace Omnipro\ConfigurableAttributes\Model\Config;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Class configurable attributes
 */
class ConfigurableAttributes
{
    /**
     * public const
     */
    public const CONFIG_PATH = 'omnipro_configurable_attributes/general/';
    public const ENABLE = 'enabled';

    /**
     * Construct
     *
     * @param ScopeConfigInterface $scopeConfig
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        protected ScopeConfigInterface $scopeConfig,
        protected StoreManagerInterface $storeManager
    )
    {}

    /**
     * Get config data
     *
     * @param $field
     * @param null $storeId
     * @return mixed
     * @throws NoSuchEntityException
     */
    public function getConfigData($field, $storeId = null): mixed
    {
        if (empty($storeId)) {
            $storeValue = $this->storeManager->getStore()->getId();
            $storeId = $storeValue ? (int)$storeValue : 0;
        }
        $path = self::CONFIG_PATH . $field;

        return $this->scopeConfig->getValue($path, ScopeInterface::SCOPE_STORE, $storeId);
    }

    /**
     * Get active
     *
     * @param null $storeId
     * @return string
     * @throws NoSuchEntityException
     */
    public function isActive($storeId = null): string
    {
        return $this->getConfigData(self::ENABLE, $storeId);
    }

}
