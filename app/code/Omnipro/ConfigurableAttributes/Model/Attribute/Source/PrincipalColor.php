<?php
/**
 *
 * @package Omnipro_ConfigurableAttributes
 * <AUTHOR> <<EMAIL>>
 */
namespace Omnipro\ConfigurableAttributes\Model\Attribute\Source;

use Magento\Catalog\Model\ResourceModel\Product\Attribute\CollectionFactory as AttributeCollectionFactory;
use Magento\Eav\Model\ResourceModel\Entity\Attribute\Option\CollectionFactory as OptionCollectionFactory;
use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;

/**
 * Class principal color
 */
class PrincipalColor extends AbstractSource
{
    /**
     * Construct
     *
     * @param AttributeCollectionFactory $attributeCollectionFactory
     * @param OptionCollectionFactory $optionCollectionFactory
     */
    public function __construct(
        protected AttributeCollectionFactory $attributeCollectionFactory,
        protected OptionCollectionFactory $optionCollectionFactory
    ) {}

    /**
     * Get all options
     *
     * @return array
     */
    public function getAllOptions(): array
    {

        $colorAttribute = $this->attributeCollectionFactory->create()
            ->addFieldToFilter('attribute_code', 'color')
            ->getFirstItem();

        if ($colorAttribute) {
            $optionCollection = $this->optionCollectionFactory->create()
                ->setAttributeFilter($colorAttribute->getId())
                ->setPositionOrder('asc', true);

            $this->_options[] = ['label' => __('Select a color'), 'value' => '0'];

            foreach ($optionCollection as $option) {
                $this->_options[] = ['label' => $option->getValue(), 'value' => $option->getId()];

            }
        }

        return $this->_options;
    }
}
