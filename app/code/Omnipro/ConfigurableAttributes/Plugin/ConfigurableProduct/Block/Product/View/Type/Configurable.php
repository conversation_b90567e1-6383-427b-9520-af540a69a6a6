<?php
/**
 *
 * @package Omnipro_ConfigurableAttributes
 * <AUTHOR> <<EMAIL>>
 */
namespace Omnipro\ConfigurableAttributes\Plugin\ConfigurableProduct\Block\Product\View\Type;

use Magento\ConfigurableProduct\Block\Product\View\Type\Configurable as ParentClass;
use Magento\Framework\Exception\NoSuchEntityException;
use Omnipro\ConfigurableAttributes\Model\Config\ConfigurableAttributes as Config;
use Magento\Framework\Serialize\Serializer\Json;

/**
 * Class configurable
 */
class Configurable
{

    /**
     * Construct
     *
     * @param Config $config
     * @param Json $json
     */
    public function __construct(
        protected Config $config,
        protected Json $json
    ) {}

    /**
     * After get json config
     *
     * @param ParentClass $subject
     * @param $result
     * @return false|string
     * @throws NoSuchEntityException
     */
    public function afterGetJsonConfig(ParentClass $subject, $result): bool|string
    {
        $resultArray = $this->json->unserialize($result, true); // Decode the JSON into an array.

        $isEnabled = $this->config->isActive();

        if (!$isEnabled) {
            return $result;
        }

        $attributes = $resultArray['attributes'];

        $principalColor = $subject->getProduct()->getPrincipalColor();

        foreach ($attributes as &$attribute) {
            foreach ($attribute['options'] as $key => $option) {
                if ($option['id'] == $principalColor) {
                    $option = $attribute['options'][$key];
                    unset($attribute['options'][$key]);
                    array_unshift($attribute['options'], $option);
                    break; // Exit the loop once the color has been moved.
                }
            }
        }

        $resultArray['attributes'] = $attributes;

        return  $this->json->serialize($resultArray);
    }
}
