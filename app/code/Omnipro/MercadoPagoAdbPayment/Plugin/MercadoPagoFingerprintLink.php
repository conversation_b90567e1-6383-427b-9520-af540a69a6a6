<?php
namespace Omnipro\MercadoPagoAdbPayment\Plugin;

use MercadoPago\AdbPayment\Gateway\Data\Checkout\Fingerprint;
use Magento\Quote\Api\Data\CartInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class MercadoPagoFingerprintLink
{
    /**
     * Method Name.
     */
    public const METHOD = 'mercadopago_base';
    /**
     * @var CartInterface
     */
    private $cart;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    public function __construct(
        CartInterface $cart,
        ScopeConfigInterface $scopeConfig,
    )
    {
        $this->cart = $cart;
        $this->scopeConfig = $scopeConfig;
        
    }

    /**
     * 
     * @param Fingerprint $subject Clase Fingerprint
     * @param string $localization Localizacion del comprador
     * @return string Link donde esta ubicado los terminos y condiciones
     */
    public function afterGetFingerPrintLink(Fingerprint $subject, $localization) {
        $storeId = $this->cart->getStoreId();
        // Ver si el plugin esta activo para este store id
        $isEnabled = boolval($this->getAddtionalValue('replace_fingerprint_link', $storeId));
        if ($isEnabled) {
            // Modificar el valor de $localization
            $localization = $this->getAddtionalValue('fingerprint_link', $storeId);
        }
        
        // Retorna $localization
        return $localization;
    }

    
    /**
     * Gets the AddtionalValues.
     *
     * @param string   $field
     * @param int|null $storeId
     * @param string   $scope
     *
     * @return string|null
     */
    protected function getAddtionalValue ($field, $storeId = null, $scope = ScopeInterface::SCOPE_STORES): ?string
    {
        $pathPattern = 'payment/%s/%s';

        return $this->scopeConfig->getValue(
            sprintf($pathPattern, self::METHOD, $field),
            $scope,
            $storeId
        );
    }
}
