<?php
namespace Omnipro\PriceRange\Plugin;

use Magento\Catalog\Block\Product\ListProduct;
use Magento\CatalogWidget\Block\Product\ProductsList;

class ProductListPlugin extends AbstractProductPricePlugin
{
    /**
     * After plugin for getProductPrice method.
     *
     * @param ListProduct|ProductsList $subject
     * @param string $result
     * @param \Magento\Catalog\Model\Product $product
     * @return string
     */
    public function afterGetProductPrice($subject, $result, $product)
    {
        return $this->getPriceRangeHtml($result, $product);
    }
}
