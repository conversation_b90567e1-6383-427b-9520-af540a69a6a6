& when (@media-common =true) {
    .catalogsearch-result-index,
    .catalog-category-view,
    .page-with-filter {
        .product-item .product-item-info .product-item-details {
            position: relative;

            .attribute-hover {
                display: none;
                position: absolute;
                top: 118px;
                left: 0;
                width: -webkit-fill-available;
                border: 1px solid #e5e5e5;
                background-color: @color-white;
                transform: translateY(-100%);
                z-index: 3;

                .color-container,
                .size-container {
                    [data-role="title"] {
                        padding: 15px 20px;
                    }

                    .color-content {
                        padding: 0 25px;

                        .swatch-option {
                            width: 15px;
                            height: 15px;

                            &:hover {
                                transform: unset;
                            }
                        }
                    }
                    .size-content .swatch-attribute {
                        .swatch-attribute-options .swatch-option {
                            display: flex;
                            flex-wrap: wrap;
                            width: 19%;
                            height: 100%;
                            margin: 0;
                            min-height: 3.5rem;
                            left: -1px;
                            font-size: 12px;
                            background: @color-white;
                            color: @color-black;
                            padding: 0;
                            justify-content: center;
                            align-content: center;

                            &:hover {
                                background-color: @color-black;
                                color: @color-white;
                                border: 1px solid @color-black;
                            }
                        }
                    }

                    button {
                        position: absolute;
                        right: 0;
                        background: white;
                        color: black;
                        font-size: 29px;
                        font-weight: bold;
                        border: none;
                        background-position: right center;
                        background-size: 16px;
                        background-repeat: no-repeat;
                        padding: 5px 27px;

                        &::before {
                            display: inline-block;
                            width: 16px;
                            height: 16px;
                            content: "";
                            background-size: cover;
                            vertical-align: super;
                        }
                    }

                    & [aria-expanded="true"] button::before {
                        background-image: url("@{baseDir}/images/icons/collapse.svg");
                        font-size: 50px;
                        height: 3px;
                    }

                    & [aria-expanded="false"] button::before {
                        background-image: url("@{baseDir}/images/icons/expand.svg");
                    }
                }

                // border top only
                .size-container {
                    border-top: 2px solid #e5e5e5;
                }
            }
        }

        .show-collapsible {
            display: block !important;
        }

        .main .swatch-attribute.size:first-child {
            display: none;
        }

        .size-content .swatch-attribute.size {
            display: block !important;
        }

        .color-content .swatch-option {
            opacity: 1 !important;
        }
    }
}
