/**
 * Copyright © Omnipro, Inc. All rights reserved.
 * See COPYING.txt for license details.
 * author : <PERSON><PERSON> <<EMAIL>>
 */
define([
    'jquery',
    'underscore'
], function ($, _) {
    'use strict';

    return function (SwatchRenderer) {
        $.widget('mage.SwatchRenderer', SwatchRenderer, {
            _init: function () {
                this._super();

                var $this = this;
                $(document).ready(function () {
                    // catalog and catalogsearch page has filter class
                    if ($('.page-with-filter').length === 1  || $('.widget-product-carousel').length === 1)  {
                        $this._RenderControlsCatalogQuickAttributes($this);
                    }
                });

            },
            _RenderControlsCatalogQuickAttributes: function ($this) {
                var $widget = $this,
                container = this.element,
                classes = this.options.classes,
                chooseText = this.options.jsonConfig.chooseText,
                showTooltip = this.options.showTooltip;

            $widget.optionsMap = {};

            $.each(this.options.jsonConfig.attributes, function () {
                var item = this,
                    controlLabelId = 'option-label-' + item.code + '-' + item.id,
                    options = $widget._RenderSwatchOptions(item, controlLabelId),
                    select = $widget._RenderSwatchSelect(item, chooseText),
                    input = $widget._RenderFormInput(item),
                    listLabel = '',
                    label = '';

                // Show only swatch controls
                if ($widget.options.onlySwatches && !$widget.options.jsonSwatchConfig.hasOwnProperty(item.id)) {
                    return;
                }

                if ($widget.options.enableControlLabel) {
                    label +=
                        '<span id="' + controlLabelId + '" class="' + classes.attributeLabelClass + '">' +
                        $('<i></i>').text(item.label).html() +
                        '</span>' +
                        '<span class="' + classes.attributeSelectedOptionLabelClass + '"></span>';
                }

                if ($widget.inProductList) {
                    $widget.productForm.append(input);
                    input = '';
                    listLabel = 'aria-label="' + $('<i></i>').text(item.label).html() + '"';
                } else {
                    listLabel = 'aria-labelledby="' + controlLabelId + '"';
                }

                // Open and Close the collapsible container
                $('.action.tocart.primary').off('click').on('click', function (e) {
                    e.preventDefault();
                    const $currentProduct = $(this).closest('li');
                    const currentHover = $currentProduct.find('.attribute-hover');
                    // close all other open containers
                    $('.attribute-hover').not(currentHover).slideUp(200);
                    // open or close the current container
                    currentHover.slideToggle(400);
                    const $colorSelected = $currentProduct.find('.main .swatch-option.color.selected');
                    if ($colorSelected.length) {
                        const colorId = $colorSelected.attr('id');
                        currentHover.find('#' + colorId).trigger('click');
                    } else {
                        currentHover.find('.swatch-option.color').first().trigger('click');
                    }
                });

                // Connect to each container
                let colorContainer = container.closest('.color-content').find('.swatch')
                let sizeContainer = container.closest('.size-content').find('.swatch')

                var swatchHtml = '<div data-role="quick" class="' + classes.attributeClass + ' ' + item.code + '" ' +
                    'data-attribute-code="' + item.code + '" ' +
                    'data-attribute-id="' + item.id + '">' +
                    label +
                    '<div aria-activedescendant="" ' +
                    'tabindex="0" ' +
                    'aria-invalid="false" ' +
                    'aria-required="true" ' +
                    'role="listbox" ' + listLabel +
                    'class="' + classes.attributeOptionsWrapper + ' clearfix">' +
                    options + select +
                    '</div>' + input +
                    '</div>';


                if (item.code === 'color') {
                    colorContainer.append(swatchHtml)
                }
                if (item.code === 'size') {
                    sizeContainer.append(swatchHtml);
                }

                // if size is clicked add to cart automatically
                sizeContainer.find('.swatch-option.text').off('click').on('click', function () {

                    let attributeContainer = $(this).closest('.attribute-hover');
                    let lastColorSwatch = attributeContainer.find('.swatch-option.color').last();

                    setTimeout(() => {
                        attributeContainer.siblings('.product-item-inner').find('form').submit();
                        // reset the attributes
                        $widget._OnClick($(this), $widget);
                        $widget._OnClick(lastColorSwatch, $widget);
                    }, 1000);

                    attributeContainer.slideToggle(400)
                });

                $widget.optionsMap[item.id] = {};

                // Aggregate options array to hash (key => value)
                $.each(item.options, function () {
                    if (this.products.length > 0) {
                        $widget.optionsMap[item.id][this.id] = {
                            price: parseInt(
                                $widget.options.jsonConfig.optionPrices[this.products[0]].finalPrice.amount,
                                10
                            ),
                            products: this.products
                        };
                    }
                });
            });

            if (showTooltip === 1) {
                // Connect Tooltip
                container
                    .find('[data-option-type="1"], [data-option-type="2"],' +
                        ' [data-option-type="0"], [data-option-type="3"]')
                    .SwatchRendererTooltip();
                }
            },
            /**
             * This Function_OnClick is like the original only changed
             * 1. Line 147 -  Dont render select functionality for color attribute and catalog page
             */
            _RenderSwatchSelect: function (config, chooseText) {
                var html,
                isCatalogPage = $('.page-with-filter').length === 1;

                // Dont render select functionality for color attribute and catalog page
                if (isCatalogPage || config.code === 'color') {
                    return '';
                }

                html =
                    '<select class="' + this.options.classes.selectClass + ' ' + config.code + '">' +
                    '<option value="0" data-option-id="0">' + chooseText + '</option>';

                $.each(config.options, function () {
                    var label = this.label,
                        attr = ' value="' + this.id + '" data-option-id="' + this.id + '"';

                    if (!this.hasOwnProperty('products') || this.products.length <= 0) {
                        attr += ' data-option-empty="true"';
                    }

                    html += '<option ' + attr + '>' + label + '</option>';
                });

                html += '</select>';

                return html;
            },
            /**
             * This Function_OnClick is like the original only changed
             * 1. Line 193 -  avoid the click on the same swatch
             */
            _OnClick: function ($this, $widget) {
                var $parent = $this.parents('.' + $widget.options.classes.attributeClass),
                    $wrapper = $this.parents('.' + $widget.options.classes.attributeOptionsWrapper),
                    $label = $parent.find('.' + $widget.options.classes.attributeSelectedOptionLabelClass),
                    attributeId = $parent.data('attribute-id'),
                    $input = $parent.find('.' + $widget.options.classes.attributeInput),
                    checkAdditionalData = JSON.parse(this.options.jsonSwatchConfig[attributeId]['additional_data']),
                    $priceBox = $widget.element.parents($widget.options.selectorProduct)
                        .find(this.options.selectorProductPrice);

                if ($widget.inProductList) {
                    $input = $widget.productForm.find(
                        '.' + $widget.options.classes.attributeInput + '[name="super_attribute[' + attributeId + ']"]'
                    );
                }

                if ($this.hasClass('disabled')) {
                    return;
                }

                /** Here we change the behavior of the original function
                 *  to avoid the click on the same swatch and leave
                 */
                $parent.attr('data-option-selected', $this.data('option-id')).find('.selected').removeClass('selected');
                $label.text($this.data('option-label'));
                $input.val($this.data('option-id'));
                $input.attr('data-attr-name', this._getAttributeCodeById(attributeId));
                $this.addClass('selected');
                $widget._toggleCheckedAttributes($this, $wrapper);
                /*  End of the change */

                $widget._Rebuild();

                if ($priceBox.is(':data(mage-priceBox)')) {
                    $widget._UpdatePrice();
                }

                $(document).trigger('updateMsrpPriceBlock',
                    [
                        this._getSelectedOptionPriceIndex(),
                        $widget.options.jsonConfig.optionPrices,
                        $priceBox
                    ]);

                if (parseInt(checkAdditionalData['update_product_preview_image'], 10) === 1) {
                    $widget._loadMedia();
                }

                $input.trigger('change');
            }

        });

        return $.mage.SwatchRenderer;
    };
});
