<?php
$productId = $this->getProductId();
?>
<div class="attribute-hover">
    <div class="color-container">
        <div data-mage-init='{"collapsible": { 
        "active": false, 
        "animate": {
        "duration": 200,
        "easing": "easeOutCubic"
    }}}'>
            <div data-role="title"><?= __('Color Name:'); ?><button data-role="trigger"></button></div>
            <div class="color-content" data-role="content">
                <div class="swatch swatch-opt-<?= $block->escapeHtmlAttr($productId) ?>" data-role="swatch-option-<?= $block->escapeHtmlAttr($productId) ?>">
                </div>
            </div>
        </div>
    </div>
    <div class="size-container">
        <div data-mage-init='{"collapsible": { 
        "active": true, 
        "animate": {
        "duration": 200,
        "easing": "easeOutCubic"
    }}}'>
            <div data-role="title"><?= __('Pick a Size'); ?><button data-role="trigger"></button></div>
            <div class="size-content" data-role="content">
                <div class="swatch swatch-opt-<?= $block->escapeHtmlAttr($productId) ?>" data-role="swatch-option-<?= $block->escapeHtmlAttr($productId) ?>">
                </div>
            </div>
        </div>
    </div>
</div>
