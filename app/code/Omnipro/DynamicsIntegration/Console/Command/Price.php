<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Omnipro\DynamicsIntegration\Console\Command;

use Magento\Framework\App\Area;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Omnipro\DynamicsIntegration\Api\PriceManagerInterface;
use Magento\Framework\App\State;

class Price extends Command
{

    const DATE_ARGUMENT = "fecha";
    const NAME_OPTION = "option";

    public function __construct(
        protected PriceManagerInterface $priceManager,
        protected State $state
    ) {

        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $output->writeln('<info>Command executed!</info>');
        $name = $input->getArgument(self::DATE_ARGUMENT);
        $isAllPrice = $input->getOption(self::NAME_OPTION);
        $this->priceManager->setIsAllPrice($isAllPrice);
        $this->priceManager->setOutput($output);
        $this->state->emulateAreaCode(Area::AREA_FRONTEND, function () {
            $this->priceManager->synchronize();
        });
        return \Magento\Framework\Console\Cli::RETURN_SUCCESS;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("omnipro:dynamicsintegration:price ");
        $this->setDescription("Price Dynamics Integration [-a]: Para recuperar todos los precios ");
        $this->setDefinition([
            new InputArgument(self::DATE_ARGUMENT, InputArgument::OPTIONAL, "Name"),
            new InputOption(self::NAME_OPTION, "-a", InputOption::VALUE_NONE, "Option functionality")
        ]);
        parent::configure();
    }
}
