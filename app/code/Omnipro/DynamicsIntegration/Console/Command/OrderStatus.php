<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Omnipro\DynamicsIntegration\Console\Command;

use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Omnipro\DynamicsIntegration\Api\OrderInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class OrderStatus extends Command
{

    public function __construct(
        protected  LoggerInterface $logger,
        protected  OrderInterface $order,
        protected State $state

    )
    {
        parent::__construct();
    }
    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $output->writeln('Iniciando el proceso...');
        $this->order->setOutput($output);

        $this->state->emulateAreaCode(Area::AREA_FRONTEND, function () {
            $this->order->managerOrderStatus();
        });
        return \Magento\Framework\Console\Cli::RETURN_SUCCESS;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("omnipro:dynamicsintegration:orderstatus");
        $this->setDescription("Order Status Dynamics Integration");
        parent::configure();
    }
}

