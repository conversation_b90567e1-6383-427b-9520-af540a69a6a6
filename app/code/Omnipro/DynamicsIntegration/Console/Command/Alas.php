<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Omnipro\DynamicsIntegration\Console\Command;

use Magento\Framework\App\Area;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Psr\Log\LoggerInterface;
use Omnipro\DynamicsIntegration\Api\OrderInterface;
use Magento\Framework\App\State;
use Alas\Integration\Observer\Orderplaceafter;
use Magento\Sales\Api\Data\OrderInterfaceFactory;
class Alas extends Command
{

    const NAME_ARGUMENT = "name";
    const NAME_OPTION = "option";

    public function __construct(
        protected  LoggerInterface $logger,
        protected  OrderInterface $order,
        protected Orderplaceafter $orderplaceafter,
        protected State $state

    )
    {
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        ;
        $incrementId = $input->getArgument(self::NAME_ARGUMENT);
        $output->writeln("Order  " . $incrementId);
        $order = $this->order->create()->loadByIncrementId($incrementId);
        $this->orderplaceafter->sendAlas($order);
        return \Magento\Framework\Console\Cli::RETURN_SUCCESS;

    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("omnipro:dynamicsintegration:alas");
        $this->setDescription("Send order to alas");
        $this->setDefinition([
            new InputArgument(self::NAME_ARGUMENT, InputArgument::OPTIONAL, "Name"),
            new InputOption(self::NAME_OPTION, "-a", InputOption::VALUE_NONE, "Option functionality")
        ]);
        parent::configure();
    }
}
