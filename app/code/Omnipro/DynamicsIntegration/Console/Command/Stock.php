<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Omnipro\DynamicsIntegration\Console\Command;

use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Omnipro\DynamicsIntegration\Api\StockManagerInterface;

class Stock extends Command
{
    const DATE_ARGUMENT = "fecha";
    const NAME_OPTION = "option";

    private StockManagerInterface $stockManager;
    protected State $state;

    /**
     * Constructor
     *
     * @param StockManagerInterface $stockManager
     * @param State $state
     */
    public function __construct(
        StockManagerInterface $stockManager,
        State $state
    ) {
        $this->stockManager = $stockManager;
        $this->state = $state;
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $output->writeln('<info>Command executed!</info>');
        $name = $input->getArgument(self::DATE_ARGUMENT);
        $isAllStock = $input->getOption(self::NAME_OPTION);
        $this->stockManager->setOutput($output);
        $this->stockManager->setIsAllStock($isAllStock);
        $this->state->emulateAreaCode(Area::AREA_FRONTEND, function () {
            $this->stockManager->synchronize();
        });
        return \Magento\Framework\Console\Cli::RETURN_SUCCESS;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName("omnipro:dynamicsintegration:stock ");
        $this->setDescription("Stock Dynamics Integration [-a]: To get all stock ");
        $this->setDefinition([
            new InputArgument(self::DATE_ARGUMENT, InputArgument::OPTIONAL, "Name"),
            new InputOption(self::NAME_OPTION, "-a", InputOption::VALUE_NONE, "Option functionality")
        ]);
        parent::configure();
    }
}
