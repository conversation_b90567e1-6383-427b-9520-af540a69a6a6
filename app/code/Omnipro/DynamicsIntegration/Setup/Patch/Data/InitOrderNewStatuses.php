<?php

namespace Omnipro\DynamicsIntegration\Setup\Patch\Data;

use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchVersionInterface;
class InitOrderNewStatuses  implements DataPatchInterface, PatchVersionInterface
{
    /**
     * @var ModuleDataSetupInterface
     */
    private $moduleDataSetup;

    /**
     * InitStatusReceived constructor.
     * @param ModuleDataSetupInterface $moduleDataSetup
     */
    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
    }

    /**
     * @inheritdoc
     */
    public function apply()
    {
        /**
         * Add received and rejected status
         */
        $this->moduleDataSetup->getConnection('sales')->insertMultiple(
            $this->moduleDataSetup->getTable('sales_order_status'),
            [
                ['status' => 'error_sync', 'label' => __(' Error in Synchronization')],
            ]
        );

        /**
         * Add received and rejected status state
         */
        $this->moduleDataSetup->getConnection('sales')->insertMultiple(
            $this->moduleDataSetup->getTable('sales_order_status_state'),
            [
                [
                    'status' => 'error_sync',
                    'state' => 'error_sync',
                    'is_default' => 1,
                    'visible_on_front' => 0,
                ]
            ]
        );

        return $this;
    }

    /**
     * @inheritdoc
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * @inheritdoc
     */
    public function getAliases()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public static function getVersion()
    {
        return '1.0.0';
    }
}