<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/cron_groups.xsd">
	<group id="dynamics">
		<!-- Frequency (in minutes) that schedules are written to the cron_schedule table -->
		<schedule_generate_every>1</schedule_generate_every>
		<!-- Time (in minutes) in advance that schedules are written to the cron_schedule table -->
		<schedule_ahead_for>4</schedule_ahead_for>
		<!-- Window of time (in minutes) that cron job must start or will be considered missed (“too late” to run) -->
		<schedule_lifetime>2</schedule_lifetime>
		<!-- Time (in minutes) that cron history is kept in the database -->
		<history_cleanup_every>10</history_cleanup_every>
		<!-- Time (in minutes) that the record of successfully completed cron jobs are kept in the database -->
		<history_success_lifetime>2880</history_success_lifetime>
		<!-- Time (in minutes) that the record of failed cron jobs are kept in the database -->
		<history_failure_lifetime>2880</history_failure_lifetime>
		<!-- Run this crongroup’s jobs in a separate php process -->
		<use_separate_process>1</use_separate_process>
	</group>
</config>
