# Changelog
All notable changes to this module will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this module adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

### Copyright:
Copyright (c) 2022 OmniPro (https://omni.pro/)

### Package:
Omnipro_DynamicsIntegration

## [1.0.4] 2024-10-08
### Fixed
- [DEPDC20-367](https://omnipro.atlassian.net/browse/DEPDC20-367)
- Issue related to invoice sending across different stores. 

## [1.0.3] 2024-10-03
### Fixed
- [DPM20-35](https://omnipro.atlassian.net/browse/DPM20-35)
- Issue about empty type_document field in Dynamics order integration.

## [1.0.2] 2023-11-28
### Added
- [DEP-310](https://omnipro.atlassian.net/browse/DEP-310)
- Added new function to model order for send (in the array 'MedioPagos') the last four digits when there is more than one payment card.

## [1.0.1] 2023-08-23

### Added
- [DEP-59](https://omnipro.atlassian.net/browse/DEP-59)
- Added header "Expect" to prevent the response with code 100 in curl.
- Added information in log file when service returns response 200 OK.
