<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Omnipro\DynamicsIntegration\Cron;

use Psr\Log\LoggerInterface;
use Omnipro\DynamicsIntegration\Api\PriceManagerInterface;

class PriceAll
{
    protected LoggerInterface $logger;
    private PriceManagerInterface $priceManager;

    /**
     * Constructor
     *
     * @param LoggerInterface $logger
     * @param PriceManager $priceManager
     */
    public function __construct(
        LoggerInterface $logger,
        PriceManagerInterface $priceManager
    ) {
        $this->logger = $logger;
        $this->priceManager = $priceManager;
    }

    /**
     * Execute the cron
     *
     * @return void
     */
    public function execute()
    {
        $this->priceManager->setIsAllPrice(true);
        $this->priceManager->synchronize();
    }
}
