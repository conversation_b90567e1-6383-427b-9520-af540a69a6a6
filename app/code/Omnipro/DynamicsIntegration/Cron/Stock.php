<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Omnipro\DynamicsIntegration\Cron;

use Omnipro\DynamicsIntegration\Api\StockManagerInterface;
use Psr\Log\LoggerInterface;

class Stock
{
    protected LoggerInterface $logger;
    private StockManagerInterface $stockManager;

    /**
     * Constructor
     *
     * @param LoggerInterface $logger
     * @param StockManager $stockManager
     */
    public function __construct(
        LoggerInterface $logger,
        StockManagerInterface $stockManager
    ) {
        $this->logger = $logger;
        $this->stockManager = $stockManager;
    }

    /**
     * Execute the cron
     *
     * @return void
     */
    public function execute()
    {
        $this->stockManager->synchronize();
    }
}
