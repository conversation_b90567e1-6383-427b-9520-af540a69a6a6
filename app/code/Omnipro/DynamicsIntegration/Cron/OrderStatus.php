<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Omnipro\DynamicsIntegration\Cron;
use Omnipro\DynamicsIntegration\Api\OrderInterface;

class OrderStatus
{

    protected $logger;

    /**
     * Constructor
     *
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Psr\Log\LoggerInterface $logger,
       protected OrderInterface $order
    )
    {
        $this->logger = $logger;
        $this->order = $order;
    }

    /**
     * Execute the cron
     *
     * @return void
     */
    public function execute()
    {
        $this->order->managerOrderStatus();
    }
}

