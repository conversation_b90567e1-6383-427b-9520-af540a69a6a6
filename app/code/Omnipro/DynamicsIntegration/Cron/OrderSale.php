<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Omnipro\DynamicsIntegration\Cron;

use Omnipro\DynamicsIntegration\Api\OrderInterface;
use Omnipro\DynamicsIntegration\Logger\Logger;



class OrderSale
{

    public function __construct(
        protected Logger $logger,
        protected OrderInterface $order
    )
    {

    }

    /**
     * Execute the cron
     *
     * @return void
     */
    public function execute()
    {
        try{
            $this->order->processOrderDynamics();
        }catch (\Exception $e){
            $this->logger->error($e->getMessage());
        }
    }
}
