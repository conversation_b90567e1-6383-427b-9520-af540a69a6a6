<?php

namespace Omnipro\DynamicsIntegration\Model;

use Magento\Framework\HTTP\Client\Curl;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Validation\ValidationException;
use Omnipro\DynamicsIntegration\Api\ConfigInterface;
use Psr\Log\LoggerInterface;
use Magento\Config\Model\Config\Backend\Encrypted;

class Client
{
    protected string $token = '';
    protected ConfigInterface $config;
    protected LoggerInterface $logger;
    protected Curl $curl;
    private SerializerInterface $serializer;
    private Encrypted $encrypted;

    public function __construct(
        ConfigInterface $config,
        LoggerInterface $logger,
        Curl $curl,
        SerializerInterface $serializer,
        Encrypted $encrypted
    ) {
        $this->config = $config;
        $this->logger = $logger;
        $this->curl = $curl;
        $this->serializer = $serializer;
        $this->encrypted = $encrypted;
    }

    /**
     * Generate access token
     *
     * @return void
     */
    public function generateToken(): void
    {
        if (empty($this->getToken())) {
            $url = $this->config->getUrlAuthenticate();
            $bodyParams = [
                'username' => $this->config->getUsername(),
                'password' => $this->encrypted->processValue($this->config->getPassword())
            ];
            $response = $this->post($url, $bodyParams);
            if ($response['status'] === 200) {
                try {
                    $this->token = $this->serializer->unserialize($response['data']);
                } catch (\Exception $e) {
                    $this->token = '';
                    $this->logger->error(
                        'An error ocurred when unserialize response token.' .
                        'response: ' . $response . ' Exception: ' . $e->getMessage()
                    );
                }
            } else {
                throw new ValidationException(__('Error when try generate token'));
            }
        }
    }

    public function generateTokenWithParams($url,$username,$password): void
    {
        if (empty($this->getToken())) {
            $bodyParams = [
                'username' => $username,
                'password' => $this->encrypted->processValue($password)
            ];
            $response = $this->post($url, $bodyParams);
            if ($response['status'] === 200) {
                try {
                    $this->token = $this->serializer->unserialize($response['data']);
                    $dataLogRespionse = [
                        "url" => $url,
                        "bodyParams" => $bodyParams,
                        "status" => $response['status'],
                        "response" => $response,
                        "token" => $this->token
                    ];
                    $this->logger->error('---> DynamicsIntegration Error -> ',  $dataLogRespionse, true);
                } catch (\Exception $e) {
                    $dataLogRespionse = [
                        "url" => $url,
                        "bodyParams" => $bodyParams,
                        "status" => $response['status'],
                        "response" => $response,
                        "token" => $this->token,
                        "exception" => array(
                            "message" => $e->getMessage(),
                            "code" => $e->getCode(),
                            "file" => $e->getFile(),
                            "line" => $e->getLine(),
                            "trace" => $e->getTraceAsString()
                        )
                    ];
                    $this->logger->error('---> DynamicsIntegration Error -> ',  $dataLogRespionse, true);

                    $this->token = '';
                    $this->logger->error(
                        'An error ocurred when unserialize response token.' .
                        'response: ' . $response . ' Exception: ' . $e->getMessage()
                    );
                }
            } else {
                $dataLogRespionse = [
                    "url" => $url,
                    "bodyParams" => $bodyParams,
                    "status" => $response['status'],
                    "response" => $response
                ];
                $this->logger->error('---> DynamicsIntegration Error -> ',  $dataLogRespionse, true);
                throw new ValidationException(__('Error when try generate token'));
            }
        }
    }


    /**
     * Do a post request
     *
     * @param string $url
     * @param array $params
     * @return array
     */
    public function post(string $url, array $params): array
    {
        $this->curl->setOptions(
            [
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_CONNECTTIMEOUT => 60,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_RETURNTRANSFER => true
            ]
        );

        $this->curl->addHeader("Content-Type", "application/json");

        if (!empty($this->getToken())) {
            $this->curl->addHeader("Authorization", $this->getToken());
            $this->curl->addHeader("Expect", "");
        }
        $this->curl->post($url, $this->serializer->serialize($params));
        return ['data' => $this->curl->getBody(), 'status' => $this->curl->getStatus()];
    }

    public function put(string $url, array $params): array
    {
        $this->curl->setOptions(
            [
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_CONNECTTIMEOUT => 60,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_CUSTOMREQUEST => 'PUT'
            ]
        );

        $this->curl->addHeader("Content-Type", "application/json");

        if (!empty($this->getToken())) {
            $this->curl->addHeader("Authorization", $this->getToken());
            $this->curl->addHeader("Expect", "");
        }
        $this->curl->post($url, $this->serializer->serialize($params));
        return ['data' => $this->curl->getBody(), 'status' => $this->curl->getStatus()];
    }


    /**
     * Do a get request
     *
     * @param string $url
     * @return array
     */
    public function get(string $url): array
    {
        $this->curl->setOptions([
            CURLINFO_HEADER_OUT => true,
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_CONNECTTIMEOUT => 60,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_RETURNTRANSFER => true
        ]);
        $this->curl->addHeader('Content-Type', 'application/json');
        $this->curl->addHeader('Authorization', $this->getToken());
        $this->curl->get($url);
        return ['data' => $this->curl->getBody(), 'status' => $this->curl->getStatus()];
    }

    /**
     * Token setter
     *
     * @param string $token
     * @return void
     */
    private function setToken(string $token): void
    {
        $this->token = $token;
    }

    /**
     * Token setter
     *
     * @return string
     */
    public function getToken(): string
    {
        return $this->token;
    }
}
