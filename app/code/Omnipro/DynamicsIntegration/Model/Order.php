<?php

namespace Omnipro\DynamicsIntegration\Model;

use Magento\Sales\Model\ResourceModel\Order\Status\History\CollectionFactory as History;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Omnipro\DynamicsIntegration\Model\Config;
use Omnipro\DynamicsIntegration\Model\Client;
use Omnipro\DynamicsIntegration\Logger\Logger;
use Magento\Framework\Serialize\SerializerInterface;
use Omnipro\DynamicsIntegration\Model\ManagerEmail;
use Magento\Sales\Model\Service\InvoiceService;
use Magento\Framework\DB\Transaction;
use Alas\Integration\Observer\Orderplaceafter;
use \Magento\Eav\Model\Config as EAVConfig;
use Magento\Sales\Model\Order as ModelOrder;
use Omnipro\DynamicsIntegration\Model\AutoShipment;
use Omnipro\DynamicsIntegration\Api\OrderInterface;
use Magento\Store\Model\StoreManagerInterface;

class Order implements OrderInterface
{

    const KEY_SHIPPING = 'normal';
    const NORMAL_DELIVERY = 0;

    const EXPRESS_DELIVERY = 'DE1';
    const ORDER_STATUS = ['complete','processing','pending'];
    const STATUS_SUCCESS = 200;
    const ERROR_STATUS = 'error_sync';
    const TYPE_IDENTIFICATION = 'type_identification';
    const IDENTIFICATION = 'identification';
    const TYPE_SERVICE = 'Despacho';
    const PAYMENT_POSITION = 1;
    const AMOUNT_FEES = 1;

    const PAYMENT_CODE = 21;

    const TYPE_CAR = 'CUENTA PERSONAL';

    const MESSAGE_LIMIT = "'¡Has llegado al Épico Límite de Intentos!, valida los datos de la orden'";

    const COURIER = "ALAS";

    const  TYPE_DOCUMENT_RUT = 'BOLETA';

    const SHIPPING = 'SHIP00-0-0';

    private $identification = '00000000';
    private $priceShipping = 0;


    const TRY_MAXIMUM = 10;

    private $output;

    private $urlSale;

    /**
     * @param CollectionFactory $collectionFactory
     * @param History $historyCollectionFactory
     * @param OrderRepositoryInterface $orderRepository
     * @param CustomerRepositoryInterface $customerRepository
     * @param \Omnipro\DynamicsIntegration\Model\Config $config
     * @param \Omnipro\DynamicsIntegration\Model\Client $client
     * @param Logger $logger
     * @param SerializerInterface $serializer
     * @param \Omnipro\DynamicsIntegration\Model\ManagerEmail $managerEmail
     * @param InvoiceService $invoiceService
     * @param Transaction $transaction
     * @param Orderplaceafter $orderplaceafter
     * @param EAVConfig $eavConfig
     * @param \Omnipro\DynamicsIntegration\Model\AutoShipment $autoShipment
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        protected CollectionFactory $collectionFactory,
        protected History $historyCollectionFactory,
        protected OrderRepositoryInterface $orderRepository,
        protected CustomerRepositoryInterface $customerRepository,
        protected Config $config,
        protected Client $client,
        protected Logger $logger,
        protected SerializerInterface $serializer,
        protected ManagerEmail $managerEmail,
        protected InvoiceService $invoiceService,
        protected Transaction $transaction,
        protected Orderplaceafter $orderplaceafter,
        protected EAVConfig $eavConfig,
        protected AutoShipment $autoShipment,
        protected StoreManagerInterface $storeManager
    ) {
    }

    /**
     * Get orders for send to ERP
     *
     * @param $status
     * @return mixed
     */
    public function getOrder()
    {
        $status = $this->config->getOrderStatus();
        $stores = $this->config->getStore();

        $condition = '(   (status in (%s))
                        or
                        (status = "pending"	and gift_cards_amount > 0 )
                      )
                        and external_id is null
                        and store_id in (%s)
        ';
        $status1 = "'" . implode("', '", $status) . "'";
        $status = "'" . implode("', '", $status) . "'";
        $query = sprintf($condition, $status1, $stores);
        $collection = $this->collectionFactory->create();
        $collection->getSelect()->where($query)
            ->order('created_at  asc');
        return $collection;
    }


    public function processOrderDynamics()
    {
        if (!$this->config->getStatus()) {
            $this->writeDebug('Module disabled');
            return false;
        }
        $this->writeDebug('Generate Token');
        $this->client->generateToken();
        $this->writeDebug('Token:' . $this->client->getToken());
        $this->urlSale = $this->config->getUrlOrder();
        $this->writeDebug('Url:' . $this->urlSale);
        $orders = $this->getOrder();
        $this->writeDebug('Total orders:' . $orders->getSize());
        /** @var  $order  \Magento\Sales\Model\Order */
        foreach ($orders as $order) {
            $reponse = $this->sendOrderToErp($order);
        }
    }


    public function sendOrderToErp($order, $force = false)
    {
        if (empty($this->urlSale)) {
            $this->urlSale = $this->config->getUrlOrder();
        }

        $this->writeDebug(sprintf('Process order: %s', $order->getIncrementId()));
        $iteration = $this->getNumberInteration($order->getStatus(), $order->getId());
        if ($iteration >= self::TRY_MAXIMUM && !$force) {
            $order->setStatus(self::ERROR_STATUS);
            $this->orderRepository->save($order);
            $order->addStatusToHistory(
                $order->getStatus(),
                sprintf('Sincronizando con ERP (%s intento)', $iteration)
            );
            if ($this->isWrite($order->getStatus(), $order->getId(), self::MESSAGE_LIMIT) == 0) {
                $order->addStatusToHistory($order->getStatus(), self::MESSAGE_LIMIT);
            }
            $this->orderRepository->save($order);
            return;
        }
        $order->addStatusToHistory($order->getStatus(), sprintf('Sincronizando con ERP (%s intento)', $iteration));
        $this->orderRepository->save($order);
        $dataOrder = $this->formatData($order);
        $report = json_encode($dataOrder, JSON_PRETTY_PRINT);
        $this->writeDebug($report);
        $response = $this->client->post($this->urlSale, $dataOrder);
        $this->writeDebug($response);
        $status = $response['status'];
        if (is_array($response) && $status != self::STATUS_SUCCESS) {
            $this->logger->error('--------------------ERROR---------------------');
            $this->logger->error($report);
            $this->logger->error(print_r($response, true));
            $message = $response['data'];
            if (!empty($message)) {
                $data = $this->serializer->unserialize($message);
                $message = (isset($data['Message'])) ? $data['Message'] : '';
                $register = sprintf('Se genero un error codigo %s detalle %s', $status, $data['Message']);
                if ($this->isWrite($order->getStatus(), $order->getId(), $register) == 0) {
                    $order->addStatusToHistory($order->getStatus(), $register);
                    $this->orderRepository->save($order);
                }
                return $data['Message'];
            }
        } else {
            if ($status == self::STATUS_SUCCESS) {
                $message = $response['data'];
                $data = $this->serializer->unserialize($message);
                $jsonHTML = $this->formatMessage($message);
                $order->addStatusToHistory($order->getStatus(), $jsonHTML);
                $order->setData('external_id', 1);
                $this->orderRepository->save($order);
                //Send email
                $this->managerEmail->sendInvoiceEmail($order, $data['url']);
                //Generate invoice
                $this->generateInvoice($order);
            }
        }
        return true;
    }


    public function formatData(\Magento\Sales\Model\Order $order)
    {
        $expres = 'shipping_express_shipping_express';
        $bodega = 0;
        $customerId = $order->getCustomerId();
        $telephone = $order->getShippingAddress()->getTelephone();
        $typeIdentification = $order->getShippingAddress()->getTypeDocument();
        $labelTypeIdentification = $this->getLabelOptionById($typeIdentification);
        $this->identification = $this->clearRut($order->getShippingAddress()->getDocument());

        if (strpos($labelTypeIdentification, 'RUT') === 0 || $labelTypeIdentification == self::TYPE_DOCUMENT_RUT) {
            if (!$this->isRUT($this->identification)) {
                $this->identification = $this->clearRut($order->getBillingAddress()->getDocument());
            }
        }
        $this->priceShipping = $order->getShippingAmount();
        /** @var  $payment \Magento\Sales\Api\Data\OrderPaymentInterface */
        $payment = $order->getPayment();
        $typeService = ($order->getShippingMethod() == $expres) ? self::EXPRESS_DELIVERY : self::NORMAL_DELIVERY;
        $data = [
            'Pedido_Id' => $order->getId(),
            'Numero_Pedido_Externo' => $order->getIncrementId(),
            'Numero_Sequence_Externo' => $order->getIncrementId(),
            'Origen' => 'MAGENTO',
            'Creation_Date' => $this->formatDate($order->getCreatedAt()),
            'Fecha_Despacho' => $this->formatDate($order->getCreatedAt()),
            'TipoServicio' => self::TYPE_SERVICE,
            'BodegaPreparacion' => $bodega,
            'Courier' => self::COURIER,
            'CourierServicio' => $typeService,
            'Monto_Total' => (float)$order->getGrandTotal(),
            'Monto_Descuento' => ($order->getDiscountAmount() > 0) ? $order->getDiscountAmount() * -1 : 0,
            'Monto_Final' => (float)$order->getGrandTotal(),
            'Tipo_Documento' => $labelTypeIdentification,
            'Cliente' => $this->formatCustomer($order, $telephone),
            'MedioPagos' => $this->formatPayment($payment),
            'Items' => $this->formatItem($order->getItems())
        ];
        return $data;
    }

    public function formatCustomer($order, $telephone)
    {
        return [
            'ClienteRut' => $this->identification,
            'ClienteNombre' => $order->getCustomerFirstname(),
            'ClienteApellido' => $order->getCustomerLastname(),
            'ClienteEmail' => $order->getCustomerEmail(),
            'ClienteTelefono' => $telephone,
            'Despacho' => $this->formatShipping($order->getShippingAddress()),
            'Facturacion' => $this->formatBillingAddress($order->getBillingAddress()),
        ];
    }

    public function formatShipping(\Magento\Sales\Api\Data\OrderAddressInterface $shipping)
    {
        $comune = $shipping->getProvinceLabel();
        if (empty($comune)) {
            $comune = $shipping->getCity();
        }

        return [
            'DirDespachoCalle' => $shipping->getStreet()[0],
            'DirDespachoNumero' => $this->onlyNumber($shipping->getNumber()) ?? '0000000000',
            'DirDespachoDepto' => $shipping->getRegionId(),
            'DirDespachoComuna' => $comune,
            'DirDespachoProvincia' => $comune,
            'DirDespachoRegion' => $shipping->getRegion(),
            'DirDespachoPais' => $shipping->getCountryId(),
            'DirDespachoCiudad' => $shipping->getCity(),
            'DirDespachoCodigoPostal' => $shipping->getPostcode(),
            'DirDespachoReferencia' => '-',
            'DirDespachoNombreContacto' => $shipping->getFirstname() . ' ' . $shipping->getLastname(),
            'DirDespachoTelefonoContacto' => $shipping->getTelephone()
        ];
    }

    public function formatBillingAddress(\Magento\Sales\Api\Data\OrderAddressInterface $shipping)
    {
        $comune = $shipping->getProvinceLabel();
        if (empty($comune)) {
            $comune = $shipping->getCity();
        }
        return [
            'DirFactRut' => $this->identification,
            'DirFactRazonSocial' => $shipping->getFirstname() . ' ' . $shipping->getLastname(),
            'DirFactCalle' => $shipping->getStreet()[0],
            'DirFactNumero' => $this->onlyNumber($shipping->getNumber()) ?? '0000000000',
            'DirFactDepto' => $shipping->getRegionId(),
            'DirFactComuna' => $comune,
            'DirFactProvincia' => $comune,
            'DirFactRegion' => $shipping->getRegion(),
            'DirFactPais' => $shipping->getCountryId(),
            'DirFactCiudad' => $shipping->getCity(),
            'DirFactCodigoPostal' => $shipping->getPostcode(),
            'DirFactReferencia' => '-',
            'DirFactNombreContacto' => $shipping->getFirstname() . ' ' . $shipping->getLastname(),
            'DirFactTelefonoContacto' => $shipping->getTelephone()
        ];
    }

    public function formatPayment(\Magento\Sales\Api\Data\OrderPaymentInterface $payment)
    {
        $dataAdditional = $payment->getAdditionalData();
        $dataInformation = $payment->getAdditionalInformation();
        $pagoCodAutorizacion = $payment->getCcTransId();
        $CcLast4 = $payment->getCcLast4();
        if (empty($pagoCodAutorizacion)) {
            $pagoCodAutorizacion = $dataInformation['mp_payment_id'] ?? 0;
        }

        $data[] = [
            'PagoCodigo' => self::PAYMENT_CODE,
            'PagoDescripcion' => $payment->getMethod(),
            'PagoMonto' => (float)$payment->getBaseAmountOrdered(),
            'PagoTipoTarjeta' => self::TYPE_CAR,
            'PagoTerminacion' => $CcLast4 ?? $this->getLast4TwoCards($dataAdditional),
            'PagoCodAutorizacion' => $pagoCodAutorizacion ?? 0,
            'PagoCuotas' => self::AMOUNT_FEES,
            'PagoPosicion' => self::PAYMENT_POSITION

        ];
        return $data;
    }

    /**
     * Get the last four digits when there is more than one payment card.
     * @param string $dataInformation
     * @return int
     */
    public function getLast4TwoCards($dataInformation)
    {
        try {
            if (!empty($dataInformation)) {
                $dataInformation = $this->serializer->unserialize($dataInformation);
                if (isset($dataInformation['transaction_info']) && is_array($dataInformation['transaction_info'])) {
                    if (!empty($dataInformation['transaction_info'][0]['card']['last_four_digits'])) {
                        return $dataInformation['transaction_info'][0]['card']['last_four_digits'];
                    }
                }
            }
            return 0;
        } catch (\Exception $e) {
            return 0;
        }
    }

    public function formatItem($items)
    {
        $rows = [];
        $i = 0;
        foreach ($items as $item) {
            if ($item->getProductType() != 'configurable') {
                continue;
            }
            $rows[] = [
                'ItemSKU' => $item->getSku(),
                'ItemEAN' => $item->getSku(),
                'ItemNombre' => $item->getName(),
                'ItemCantidad' => (int)$item->getQtyOrdered(),
                'ItemPrecioOriginal' => (float)$item->getBasePriceInclTax(),
                'ItemPrecioFinal' => (float)$item->getBasePriceInclTax(),
                'ItemMontoDescuento' => (float)$item->getDiscountAmount(),
                'ItemSubtotal' => (float)$item->getRowTotalInclTax() - (float)$item->getDiscountAmount(),
                'ItemWarehouseId' => 0,
                'ItemPosicion' => $i++,
            ];
        }
        //Set Shipping
        $rows[] = [
            'ItemSKU' => self::SHIPPING,
            'ItemEAN' => self::SHIPPING,
            'ItemNombre' => 'Costo de envio',
            'ItemCantidad' => 1,
            'ItemPrecioOriginal' => (float)$this->priceShipping,
            'ItemPrecioFinal' => (float)$this->priceShipping,
            'ItemMontoDescuento' => 0,
            'ItemSubtotal' => (float)$this->priceShipping,
            'ItemWarehouseId' => 0,
            'ItemPosicion' => $i++,
        ];


        return $rows;
    }


    public function formatDate($date)
    {
        $fecha_objeto = \DateTime::createFromFormat('Y-m-d H:i:s', $date);
        $microsegundos = $fecha_objeto->format('u');
        return $fecha_objeto->format('Y-m-d\TH:i:s.') . $microsegundos . $fecha_objeto->format('P');
    }

    /**
     * Delete the registers of interations with SAP and return the number of interations
     *
     * @param string $status
     * @param int $orderId
     * @return int
     * <AUTHOR> Hernan Aguilar <<EMAIL>>
     */
    public function getNumberInteration($status = 'pending', $orderId = 123456)
    {
        $iterations = 0;
        $historiesModel = $this->historyCollectionFactory->create();
        $historiesModel->addFieldToFilter('entity_name', 'order');
        $historiesModel->addFieldToFilter('status', ['in' => $status]);
        $historiesModel->addFieldToFilter('parent_id', $orderId);
        $historiesModel->addFieldToFilter('comment', ['like' => '%Sincroni%']);
        $historiesModel->load();

        foreach ($historiesModel as $history) {
            $iterations = intval(preg_replace('/[^0-9]+/i', '', $history->getData('comment')));
            $history->delete();
        }
        $historiesModel->save();
        return ++$iterations;
    }


    private function writeDebug($message): void
    {
        if ($this->output) {
            if (is_string($message)) {
                $this->output->writeln($message);
            } else {
                $this->output->writeln(print_r($message, true));
            }
        }
    }

    /**
     * @param mixed $output
     */
    public function setOutput(\Symfony\Component\Console\Output\OutputInterface $output): void
    {
        $this->output = $output;
    }

    public function isWrite($status = 'pending', $orderId = 123456, $message = '')
    {
        $historiesModel = $this->historyCollectionFactory->create();
        $historiesModel->addFieldToFilter('entity_name', 'order');
        $historiesModel->addFieldToFilter('status', ['in' => $status]);
        $historiesModel->addFieldToFilter('parent_id', $orderId);
        $historiesModel->addFieldToFilter('comment', ['like' => '%' . $message . '%']);
        return $historiesModel->getTotalCount();
    }

    /**
     * @param $jsonString
     * @return string
     */
    public function formatMessage($jsonString)
    {
        $jsonData = $this->serializer->unserialize($jsonString);
        $formattedJson = '';
        foreach ($jsonData as $key => $value) {
            if ($key == 'url') {
                $value = sprintf('<a href="%s" target=_blank">%s</a>', $value, $value);
            }
            $formattedJson .= "<strong>{$key}:</strong> {$value}, <br>";
        }
        $formattedJson = rtrim($formattedJson, ', ');

        return $formattedJson;
    }

    /**
     * Get all stores
     *
     * @return array
     */
    private function getStoreIds()
    {
        return array_keys($this->storeManager->getStores());
    }

    public function getOrderStatus()
    {
        $storeIds = $this->getStoreIds();
        $stores = [];
        foreach ($storeIds ?? [] as $storeId) {
            if ($this->config->getStatusBoleta((int)$storeId)) {
                $stores[] = $storeId;
            }
        }

        $now = new \DateTime();
        $sevenDaysAgo = $now->modify('-15 days')->format('Y-m-d H:i:s');
        return $this->collectionFactory->create()
            ->addAttributeToFilter('status', ['in' => self::ORDER_STATUS])
            ->addAttributeToFilter('external_id', ['neq' => 'NULL'])
            ->addAttributeToFilter('notification_email', ['null' => true])
            ->addAttributeToFilter('store_id', ['in' => $stores])
            ->addFieldToFilter('created_at', ['gteq' => $sevenDaysAgo])
            ->setOrder('created_at', 'ASC');
    }


    public function managerOrderStatus()
    {
        if (!$this->config->getStatus()) {
            $this->writeDebug('Module disabled');
            return false;
        }
        $this->client->generateToken();
        $orders = $this->getOrderStatus();
        $url = $this->config->getUrlOrderInfo();
        $this->writeDebug('Total order:' . $orders->getTotalCount());
        foreach ($orders as $order) {
            $urlConsulOrder = $url . '?Nro_Order=' . $order->getIncrementId() . '&Origen=Magento';
            $this->writeDebug($urlConsulOrder);
            $response = $this->client->get($urlConsulOrder);
            if ($response['status'] == self::STATUS_SUCCESS) {
                $data = $this->serializer->unserialize($response['data']);
                if ($data['StatusID'] >= 1) {
                    //Send email
                    $this->managerEmail->sendNotificationPickup($order);
                    $confirmation = 'StatusID:' . $data['StatusID'];
                    $message = 'El correo electrónico ha sido enviado para notificar sobre la preparación del pedido.';
                    $order->addStatusToHistory($order->getStatus(), $message);
                    $order->setData('notification_email', $confirmation);
                    //Send Alas
                    $alasSend = $this->orderplaceafter->sendAlas($order);
                    $this->orderRepository->save($order);
                    // Generate SHIP
                    $this->generateShip($order);
                }
            }
            $this->writeDebug($response);
        }
    }

    /**
     * Generate Invoice
     *
     * @param $order
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function generateInvoice($order)
    {
        if ($order->canInvoice()) {
            // Create an invoice
            $invoice = $this->invoiceService->prepareInvoice($order);
            $invoice->setState(\Magento\Sales\Model\Order\Invoice::STATE_PAID);
            $invoice->setStatus(\Magento\Sales\Model\Order\Invoice::STATE_PAID);
            $this->transaction
                ->addObject($invoice)
                ->addObject($invoice->getOrder());
            $this->transaction->save();
        }
    }

    /**
     * Get label of option id for the type_document
     * @param $optionId
     * @return mixed|string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getLabelOptionById($optionId)
    {
        $attributeCode = 'type_document';
        $attribute = $this->eavConfig->getAttribute('customer_address', $attributeCode);
        $options = $attribute->getSource()->getAllOptions();
        $labels = [];
        foreach ($options as $option) {
            $labels[$option['value']] = trim($option['label']);
        }

        return isset($labels[$optionId]) ? $labels[$optionId] : self::TYPE_DOCUMENT_RUT;
    }


    /**
     * Generate ShIP
     * @param $order
     * @return void
     */
    public function generateShip($order)
    {
        /**
         * @var ModelOrder $order
         */
        if ($order->getShippingMethod() === 'instore_pickup') {
            return;
        }

        $shippingDescription = $order->getShippingDescription();
        //Determina la fuente de inventario que se utilizara para la generacion del SHIP en magento
        $source = $this->config->getAvailableSource($order->getStoreId());
        $this->autoShipment->generateShipmentWithComment(
            $order->getId(),
            $source
        );
    }

    private function isRUT($rut): bool
    {
        $pattern = "/^(\d{7,8})-([\d|k|K])$/";
        return preg_match($pattern, $rut);
    }

    private function clearRut($number)
    {
        return preg_replace('/[^0-9-K]/', '', strtoupper($number));
    }

    private function onlyNumber($number)
    {
        if (!empty($number)) {
            return preg_replace('/[^0-9]/', '', $number);
        }
        return 0;
    }


}
