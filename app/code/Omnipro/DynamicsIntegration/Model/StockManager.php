<?php

namespace Omnipro\DynamicsIntegration\Model;

use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\Validation\ValidationException;
use Magento\InventoryApi\Api\Data\SourceItemInterface;
use Magento\InventoryApi\Api\SourceItemsSaveInterface;
use Magento\InventorySourceDeductionApi\Model\GetSourceItemBySourceCodeAndSku;
use Omnipro\DynamicsIntegration\Api\StockManagerInterface;
use Omnipro\DynamicsIntegration\Logger\Logger;
use Omnipro\DynamicsIntegration\Model\Client;
use Omnipro\DynamicsIntegration\Api\ConfigInterface;
use Magento\InventoryApi\Api\Data\SourceItemInterfaceFactory;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Store\Model\StoreManagerInterface;

class StockManager implements StockManagerInterface
{
    private const SOURCE_CODE = 'fuente_1';
    private const SOURCE_CODE_EXPRESS = 'fuente_2';
    private const WAREHOUSE_ID = '0';

    private $isAllStock = false;
    private $output;
    private $productsNotFound = [];
    private $count = 0;

    /**
     * @var Client
     */
    private $client;

    /**
     * @var Config
     */
    private $config;

    /**
     * @var SerializerInterface
     */
    private $serializer;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * @var TimezoneInterface
     */
    private $timezone;

    /**
     * @var SourceItemsSaveInterface
     */
    private $sourceItemsSave;

    /**
     * @var GetSourceItemBySourceCodeAndSku
     */
    private $getSourceItemBySourceCodeAndSku;
    private SourceItemInterfaceFactory $sourceItemInterfaceFactory;
    private ProductRepositoryInterface $productRepository;
    private StoreManagerInterface $storeManager;

    /**
     * Constructor
     *
     * @param Client $client
     * @param Config $config
     * @param SerializerInterface $serializer
     * @param Logger $logger
     * @param TimezoneInterface $timezone
     * @param SourceItemsSaveInterface $sourceItemsSave
     * @param GetSourceItemBySourceCodeAndSku $getSourceItemBySourceCodeAndSku
     */
    public function __construct(
        Client $client,
        ConfigInterface $config,
        SerializerInterface $serializer,
        Logger $logger,
        TimezoneInterface $timezone,
        SourceItemsSaveInterface $sourceItemsSave,
        GetSourceItemBySourceCodeAndSku $getSourceItemBySourceCodeAndSku,
        SourceItemInterfaceFactory $sourceItemInterfaceFactory,
        ProductRepositoryInterface $productRepository,
        StoreManagerInterface $storeManager
    ) {
        $this->client = $client;
        $this->config = $config;
        $this->serializer = $serializer;
        $this->logger = $logger;
        $this->timezone = $timezone;
        $this->sourceItemsSave = $sourceItemsSave;
        $this->getSourceItemBySourceCodeAndSku = $getSourceItemBySourceCodeAndSku;
        $this->sourceItemInterfaceFactory = $sourceItemInterfaceFactory;
        $this->productRepository = $productRepository;
        $this->storeManager = $storeManager;
    }

    /**
     * Synchronizes the stock in every source
     *
     * @return bool
     */
    public function synchronize()
    {
        if (!$this->config->getStatus()) {
            $this->logger->info('Module disabled');
            return false;
        }
        $this->writeDebug('Source: ' . self::SOURCE_CODE . ' Warehouse ID: ' . self::WAREHOUSE_ID);
        $stockData = $this->getStockData(self::WAREHOUSE_ID);
        $this->proccessSock($stockData);
        $this->writeDebug('Total products updated:' . $this->count);
        $this->writeDebug('Total products not found:' . count($this->productsNotFound));
        return true;
    }

    public function proccessSock($stockData): void
    {
        foreach ($stockData as $productData) {
            try {
                $product = $this->productRepository->get($productData['Item']);
            } catch (NoSuchEntityException $e) {
                $this->productsNotFound[] = $productData['Item'];
                continue;
            }

            $source = [
                self::SOURCE_CODE => 'Stock_ecom',
                self::SOURCE_CODE_EXPRESS => 'Stock_Express'
            ];

            foreach ($source as $fuente => $stock) {
                try {
                    $sourceItem = $this->getSourceItemBySourceCodeAndSku->execute(
                        $fuente,
                        $productData['Item']
                    );
                    $sourceItem->setQuantity($productData[$stock]);
                    $sourceItem->setStatus(
                        $productData[$stock] > 0 ? SourceItemInterface::STATUS_IN_STOCK : SourceItemInterface::STATUS_OUT_OF_STOCK
                    );
                    $this->sourceItemsSave->execute([$sourceItem]);
                    $this->count++;
                } catch (NoSuchEntityException $e) {
                    $this->addStock($productData['Item'], $productData[$stock], $fuente);
                } catch (\Exception $e) {
                    $this->logger->error('[Model\Stock::getStock] - ' . $e->getMessage());
                }
            }
        }
    }


    /**
     * Get stock data for a given source
     *
     * @param string $sourceCode
     * @return array
     */
    private function getStockData(string $sourceCode): array
    {
        $result = [];
        $this->client->generateToken();
        $this->writeDebug('Token: ' . $this->client->getToken());
        $url = $this->getServiceUrl($sourceCode);
        $this->writeDebug('Url: ' . $url);
        $response = $this->client->get($url);
        $statusCode = $response['status'];

        if ($statusCode === 200) {
            try {
                $result = $this->serializer->unserialize($response['data']);
            } catch (\Exception $e) {
                $this->logger->error('[Model\Stock::getStockData] - ' . $e->getMessage());
            }
        } else {
            $this->logger->error(
                '[Model\Stock::getStockData] - An error occurred while requesting stock data. Status code: ' . $statusCode
            );
        }
        return $result;
    }

    /**
     * Get full stock service url
     *
     * @param string $warehouseId
     * @return string
     */
    private function getServiceUrl(string $warehouseId): string
    {
        $url = $this->config->getUrlStock();
        $currentDate = urlencode($this->getCurrentDate());
        $this->writeDebug('Hora:' . $currentDate);
        if ($this->isAllStock()) {
            $currentDate = '1900-01-01';
        }
        return $url . '?warehouse_id=' . $warehouseId . '&DateInventory=' . $currentDate . '&storeWeb_id=6';
    }

    /**
     * Get current date in Chile timezone in format AAAA-MM-DD HH:MM
     *
     * @return string
     */
    private function getCurrentDate(): string
    {
        $store = $this->storeManager->getStore($this->config->getStore());
        $storeTimezone = $store->getConfig('general/locale/timezone');
        $currentUtcTime = new \DateTime('now', new \DateTimeZone('UTC'));
        $santiagoTimezone = new \DateTimeZone($storeTimezone);
        $currentSantiagoTime = new \DateTime('now', $santiagoTimezone);
        $timeOffset = $santiagoTimezone->getOffset($currentUtcTime) / 3600;
        $currentSantiagoTime->sub(new \DateInterval('PT' . abs($timeOffset) . 'H'));
        return $currentSantiagoTime->format('Y-m-d H:00');
    }

    /**
     * @return bool
     */
    public function isAllStock(): bool
    {
        return $this->isAllStock;
    }

    /**
     * @param bool $isAllStock
     */
    public function setIsAllStock(bool $isAllStock): void
    {
        $this->isAllStock = $isAllStock;
    }

    private function writeDebug($message): void
    {
        if ($this->output) {
            if (is_string($message)) {
                $this->output->writeln($message);
            } else {
                $this->output->writeln(print_r($message, true));
            }
        }
    }

    /**
     * @param mixed $output
     */
    public function setOutput(\Symfony\Component\Console\Output\OutputInterface $output): void
    {
        $this->output = $output;
    }

    /**
     * Register Stock
     *
     * @param $sku
     * @param $qty
     * @param $sourceCode
     * @return void
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Validation\ValidationException
     */
    private function addStock($sku, $qty, $sourceCode): void
    {
        $inStock = ($qty > 0) ? 1 : 0;
        $sourceItem = $this->sourceItemInterfaceFactory->create();
        $sourceItem->setSku((string)$sku);
        $sourceItem->setSourceCode($sourceCode);
        $sourceItem->setQuantity((float)$qty);
        $sourceItem->setStatus((int)$inStock);
        $sourceItems[] = $sourceItem;
        $this->sourceItemsSave->execute($sourceItems);
    }
}
