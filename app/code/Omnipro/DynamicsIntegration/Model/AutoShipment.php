<?php
namespace Omnipro\DynamicsIntegration\Model;

use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Api\ShipmentRepositoryInterface;
use Magento\Sales\Api\Data\ShipmentInterfaceFactory;
use Magento\Sales\Model\Order\ShipmentFactory;
use Magento\Inventory\Model\ResourceModel\Source\CollectionFactory as SourceCollectionFactory;
use Magento\InventoryApi\Api\SourceRepositoryInterface;
use Magento\InventoryApi\Api\StockRepositoryInterface;
use Psr\Log\LoggerInterface;
use Magento\Sales\Model\Convert\Order;

class AutoShipment
{
    protected $orderRepository;
    protected $shipmentRepository;
    protected $shipmentFactory;
    protected $shipmentItemFactory;
    protected $sourceCollectionFactory;
    protected $sourceRepository;
    protected $stockRepository;
    protected $historyFactory;
    private LoggerInterface $logger;
    private Order $convertOrder;

    public function __construct(
        OrderRepositoryInterface $orderRepository,
        ShipmentRepositoryInterface $shipmentRepository,
        ShipmentInterfaceFactory $shipmentItemFactory,
        ShipmentFactory $shipmentFactory,
        SourceCollectionFactory $sourceCollectionFactory,
        SourceRepositoryInterface $sourceRepository,
        StockRepositoryInterface $stockRepository,
        \Magento\Sales\Model\Order\Status\HistoryFactory $historyFactory,
        LoggerInterface $logger,
        Order $convertOrder
    ) {
        $this->orderRepository = $orderRepository;
        $this->shipmentRepository = $shipmentRepository;
        $this->shipmentItemFactory = $shipmentItemFactory;
        $this->shipmentFactory = $shipmentFactory;
        $this->sourceCollectionFactory = $sourceCollectionFactory;
        $this->sourceRepository = $sourceRepository;
        $this->stockRepository = $stockRepository;
        $this->historyFactory = $historyFactory;
        $this->logger = $logger;
        $this->convertOrder = $convertOrder;
    }

    public function generateShipmentWithComment($orderId, $sourceCode,$message='')
    {
        try {

            $order = $this->orderRepository->get($orderId);
            $shipment = $this->shipmentFactory->create($order);

            foreach ($order->getItems() as $orderItem) {
                if (!$orderItem->getQtyToShip() || $orderItem->getIsVirtual()) {
                    continue;
                }
                $qty = $orderItem->getQtyToShip();
                $shipmentItem = $this->convertOrder
                    ->itemToShipmentItem($orderItem)
                    ->setQty($qty);
                $shipment->addItem($shipmentItem);
            }
            $source = $this->sourceRepository->get($sourceCode);
            $shipment->getExtensionAttributes()->setSourceCode($source->getSourceCode());
            $shipment->register();
            $this->shipmentRepository->save($shipment);
            $shipment->getOrder()->save();
            if(!empty($message)) {
                $order->addStatusToHistory($order->getStatus(), $message);
                $this->orderRepository->save($order);
            }

            return $shipment->getEntityId();
        } catch (LocalizedException $e) {
            $this->logger->error('Error al generar el envío: ' . $e->getMessage());
            return false;
        }
    }
}
