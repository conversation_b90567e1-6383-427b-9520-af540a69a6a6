<?php

namespace Omnipro\DynamicsIntegration\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Omnipro\DynamicsIntegration\Api\ConfigInterface;

class Config implements ConfigInterface
{
    public const XML_PATH_LIST_DEFAULT_USERNAME = 'dynamics/setting/username';
    public const XML_PATH_LIST_DEFAULT_STATUS = 'dynamics/setting/status';
    public const XML_PATH_LIST_DEFAULT_PASSWORD = 'dynamics/setting/password';
    public const XML_PATH_LIST_DEFAULT_INFO = 'dynamics/setting/info';
    public const XML_PATH_LIST_DEFAULT_BASE = 'dynamics/setting/base';
    public const XML_PATH_LIST_DEFAULT_AUTHENTICATE = 'dynamics/setting/authenticate';
    public const XML_PATH_LIST_DEFAULT_STATUS_PRICE = 'dynamics/price/status_price';
    public const XML_PATH_LIST_DEFAULT_PRICE_URL = 'dynamics/price/service_url';
    public const XML_PATH_LIST_DEFAULT_PRICE_STORE_ID = 'dynamics/price/store_web_id';
    public const XML_PATH_LIST_DEFAULT_ORDER_STATUS = 'dynamics/setting/order_status';
    public const XML_PATH_LIST_DEFAULT_STATUS_BOLETA = 'dynamics/setting/status_boleta';
    public const XML_PATH_LIST_DEFAULT_STOCK_URL = 'dynamics/setting/stock';
    public const XML_PATH_LIST_DEFAULT_SALES = 'dynamics/setting/sale';

    public const XML_PATH_LIST_DEFAULT_STORE = 'dynamics/setting/store';
    public const XML_PATH_AVAILABLE_SOURCE = 'shipmentgenerate/setting/excluded_warehouses';



    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var EncryptorInterface
     */
    private $encryptor;

    public function __construct(ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @param $store
     * <AUTHOR> Hernan Aguilar Hurtado<<EMAIL>>
     * @return string
     */
    public function getUsername($store = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_USERNAME,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
        );
    }

    /**
     * @param $store
     * <AUTHOR> Hernan Aguilar Hurtado<<EMAIL>>
     * @return string
     */
    public function getStatus($store = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_STATUS,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
        );
    }

    /**
     * Getter for status boleta
     *
     * @param int $storeId
     * @return bool
     */
    public function getStatusBoleta(int $storeId): bool{
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_LIST_DEFAULT_STATUS_BOLETA,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param $store
     * <AUTHOR> Hernan Aguilar Hurtado<<EMAIL>>
     * @return string
     *
     */
    public function getPassword($store = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_PASSWORD,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
        );
    }

    public function getBaseUrl($store = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_BASE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
        );
    }

    /**
     * Return the url for authenticate
     *
     * <AUTHOR> Hernan Aguilar Hurtado<<EMAIL>>
     * @return string
     */
    public function getUrlAuthenticate(){
        $baseUrl =  $this->getBaseUrl();
        $authUrl = $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_AUTHENTICATE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        return $baseUrl . $authUrl;
    }

    /**
     * Return status of prices integration
     *
     * @param $store
     * <AUTHOR> Benavides <<EMAIL>>
     * @return string
     */
    public function isEnabledPriceIntegration($store = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_STATUS_PRICE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
        );
    }

    public function getWebStoreId($store = null)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_PRICE_STORE_ID,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
        );
    }

    /**
     * Return the url for prices service
     *
     * <AUTHOR> Benavides <<EMAIL>>
     * @return string
     */
    public function getUrlPrice(): string
    {
        $baseUrl =  $this->getBaseUrl();
        $authUrl = $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_PRICE_URL,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        return $baseUrl . $authUrl;
    }

    public function getOrderStatus($store = null)
    {
        $status =  $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_ORDER_STATUS,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
        );

        if(!empty($status)){
            return  explode(',',$status);
        }else{
            return  [];
        }
    }

    public function getStore($store = null){
        return  $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_STORE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
        );
    }

    public function getUrlOrder(){
        $baseUrl =  $this->getBaseUrl();
        $url = $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_SALES,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        return $baseUrl . $url;
    }

    /**
     * Return the url for stock service
     *
     * <AUTHOR> Benavides <<EMAIL>>
     * @return string
     */
    public function getUrlStock(): string
    {
        $baseUrl =  $this->getBaseUrl();
        $authUrl = $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_STOCK_URL,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        return $baseUrl . $authUrl;
    }

    public function getCurrentDateTime(): string
    {
        return (new \DateTime())->format(\Magento\Framework\Stdlib\DateTime::DATE_PHP_FORMAT);
    }

    public function getUrlOrderInfo(){
        $baseUrl =  $this->getBaseUrl();
        $url = $this->scopeConfig->getValue(
            self::XML_PATH_LIST_DEFAULT_INFO,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        return $baseUrl . $url;
    }

    public function getAvailableSource($storeId)
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_AVAILABLE_SOURCE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE, $storeId
        );
    }
}
