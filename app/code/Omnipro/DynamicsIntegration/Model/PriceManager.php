<?php

namespace Omnipro\DynamicsIntegration\Model;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Omnipro\DynamicsIntegration\Api\PriceManagerInterface;
use Omnipro\DynamicsIntegration\Model\Client;
use Omnipro\DynamicsIntegration\Model\Config;
use Omnipro\DynamicsIntegration\Logger\Logger;

class PriceManager implements PriceManagerInterface
{
    private const PRICELIST = 'ECOM';
    private const STOREWEB_ID = 1;

    private $output ;

    /**
     * @var Client
     */
    private $client;

    /**
     * @var Config
     */
    private $config;

    /**
     * @var SerializerInterface
     */
    private $serializer;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * @var TimezoneInterface
     */
    private $timezone;

    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;

    private $isAllPrice = false;

    /**
     * Constructor
     *
     * @param Client $client
     * @param Config $config
     * @param SerializerInterface $serializer
     * @param Logger $logger
     * @param TimezoneInterface $timezone
     * @param ProductRepositoryInterface $productRepository
     */
    public function __construct(
        Client $client,
        Config $config,
        SerializerInterface $serializer,
        Logger $logger,
        TimezoneInterface $timezone,
        ProductRepositoryInterface $productRepository
    ) {
        $this->client = $client;
        $this->config = $config;
        $this->serializer = $serializer;
        $this->logger = $logger;
        $this->timezone = $timezone;
        $this->productRepository = $productRepository;
    }

    /**
     * Synchronize products prices with prices integration
     *
     * @return void
     */
    public function synchronize()
    {
        if ($this->config->isEnabledPriceIntegration()) {
            $products = $this->getPrices();
            $store = $this->config->getStore();
            $this->writeDebug('Store:' .$store);
            $this->writeDebug('Total product ' . count($products));
            $productNoFount = [];
            $count = 0;
            foreach ($products as $productItem) {
                try {
                    $product = $this->productRepository->get($productItem['Item'], true, $store);
                    $product->setPrice((float) $productItem['FullPrice']);
                    $product->setSpecialPrice((float) $productItem['SalePrice']);
                    $this->productRepository->save($product);
                    $count++;
                } catch (\Exception $e) {
                    $productNoFount[] = $productItem['Item'];
                    $this->logger->error($e->getMessage());
                }
            }
            $this->writeDebug('Total products updated:' . $count);
            $this->writeDebug('Total products no found:' . count($productNoFount));
        }else{
            $this->writeDebug('Module disabled');
        }
    }

    /**
     * Get updated prices data
     *
     * @return array|string
     */
    public function getPrices()
    {
        $result = [];
        $url = $this->getServiceUrl();
        $this->writeDebug('Url: ' . $url);
        $this->client->generateToken();
        $response =  $this->client->get($url);
        $statusCode = $response['status'];

        if ($statusCode === 200) {
            try {
                $result = $this->serializer->unserialize($response['data']);
            } catch (\Exception $e) {
                $this->logger->error($e->getMessage());
            }
        } else {
            $this->logger->error('An error occurred while requesting prices data. Status code: ' . $statusCode);
        }
        return $result;
    }

    /**
     * Get full prices service url
     *
     * @return string
     */
    public function getServiceUrl(): string
    {
        $url = $this->config->getUrlPrice();
        $currentDate = $this->config->getCurrentDateTime();
        if($this->isAllPrice()){
            $currentDate = '1900-01-01';
        }
        $storeId = $this->config->getWebStoreId();
        return $url . '?PriceList=' . self::PRICELIST . '&DatePrice=' . $currentDate . '&storeWeb_id=' . $storeId;
    }

    private function writeDebug($message):void{
        if($this->output){
            if(is_string($message)){
                $this->output->writeln($message);
            }else{
                $this->output->writeln(print_r($message,true));
            }
        }
    }

    /**
     * @param mixed $output
     */
    public function setOutput(\Symfony\Component\Console\Output\OutputInterface $output): void
    {
        $this->output = $output;
    }

    /**
     * @return bool
     */
    public function isAllPrice(): bool
    {
        return $this->isAllPrice;
    }

    /**
     * @param bool $isAllPrice
     */
    public function setIsAllPrice(bool $isAllPrice): void
    {
        $this->isAllPrice = $isAllPrice;
    }


}
