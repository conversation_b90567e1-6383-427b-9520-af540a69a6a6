<?php

namespace Omnipro\DynamicsIntegration\Model;

use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Omnipro\DynamicsIntegration\Logger\Logger;
use Magento\Sales\Model\Order\Email\Container\OrderIdentity as IdentityInterface;
use  Magento\Email\Model\TemplateFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\LocalizedException;
class ManagerEmail
{
    PUBLIC CONST COPY_METHOD = 'bcc';

    public function __construct(
        protected TransportBuilder $transportBuilder,
        protected StateInterface $inlineTranslation,
        protected Logger $logger,
        protected IdentityInterface $identityContainer,
        protected  TemplateFactory $templateFactory
    )
    {
    }

    public function sendInvoiceEmail($order, $url):void
    {
        $recipientName = $order->getCustomerFirstname() . ' ' .  $order->getCustomerLastName();

        if (!filter_var($url, FILTER_VALIDATE_URL) || trim($recipientName) == '') {
            return;
        }

        $templateId = 'dynamics_email_order_invoice';
        $templateParams = [
            'order' => $order,
            'url_pdf' => $url,
            'name' => $recipientName
        ];
        $this->send($order,$templateId,$templateParams);
    }

    public function sendNotificationPickup($order):void{
        $templateId = 'dynamics_email_order_picking';


        $recipientName = $order->getCustomerFirstname() . ' ' .  $order->getCustomerLastName();
        $templateParams = [
            'order' => $order,
            'name' => $recipientName
        ];
        $this->send($order,$templateId,$templateParams);
    }



    public function  send($order,$templateId,$templateParams):void{

        $templateId = $this->getLatestTemplateByOrigTemplateCode($templateId);
        $this->inlineTranslation->suspend();
        $recipientName = $order->getCustomerFirstname() . ' ' .  $order->getCustomerLastName();
        $recipientEmail = $order->getCustomerEmail();
        $storeId = $order->getStoreId();
        try {
            // Lines for displaying hidden copies of the order invoice
            $copyTo = $this->identityContainer->getEmailCopyTo();
            if (!empty($copyTo) && $this->identityContainer->getCopyMethod() == self::COPY_METHOD) {
                foreach ($copyTo as $email) {
                    $this->transportBuilder->addBcc($email);
                }
            }

            $transport = $this->transportBuilder->setTemplateIdentifier($templateId
            )->setTemplateOptions(
                ['area' => \Magento\Framework\App\Area::AREA_FRONTEND, 'store' => $storeId]
            )->setTemplateVars(
                $templateParams
            )->setFromByScope(
                'general',
                $storeId
            )->addTo($recipientEmail, $recipientName)
             ->getTransport();
            $transport->sendMessage();
        } catch (\Exception $e) {
            $this->logger->error('ManagerEmail' . $e->getMessage());
        }
    }


    public function getLatestTemplateByOrigTemplateCode($origTemplateCode)
    {
        try {
            $templateModel = $this->templateFactory->create();
            $templates = $templateModel->getCollection()
                ->addFieldToFilter('orig_template_code', $origTemplateCode)
                ->setOrder('template_id', 'DESC')
                ->setPageSize(1);
            $template = $templates->getFirstItem();
            if ($template->getId()) {
                return $template->getId();
            } else {
                return $origTemplateCode;
            }
        } catch (NoSuchEntityException $e) {
            return $origTemplateCode;
        } catch (LocalizedException $e) {
            // Handle other exceptions
            return $origTemplateCode;
        }
    }


}
