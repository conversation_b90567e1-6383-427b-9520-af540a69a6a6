# Mage2 Module Omnipro DynamicsIntegration

    ``omnipro/module-dynamicsintegration``

 - [Main Functionalities](#markdown-header-main-functionalities)
 - [Installation](#markdown-header-installation)
 - [Configuration](#markdown-header-configuration)
 - [Specifications](#markdown-header-specifications)
 - [Attributes](#markdown-header-attributes)


## Main Functionalities
Integration  Dynamics base the API https://documenter.getpostman.com/view/5709262/2s93Y2Sh8k#ba68078e-bc46-4f8d-92e5-d3e23257d673

## Installation
\* = in production please use the `--keep-generated` option

### Type 1: Zip file

 - Unzip the zip file in `app/code/Omnipro`
 - Enable the module by running `php bin/magento module:enable Omnipro_DynamicsIntegration`
 - Apply database updates by running `php bin/magento setup:upgrade`\*
 - Flush the cache by running `php bin/magento cache:flush`

## Configuration

 - username (dynamics/setting/username)

 - password (dynamics/setting/password)

 - status (dynamics/setting/status)


## Specifications

 - Console Command
	- Stock

 - Console Command
	- Price

 - Console Command
	- OrderSale

 - Console Command
	- OrderStatus

 - Crongroup
	- dynamics

 - Cronjob
	- omnipro_dynamicsintegration_stock

 - Cronjob
	- omnipro_dynamicsintegration_price

 - Cronjob
	- omnipro_dynamicsintegration_ordersale

 - Cronjob
	- omnipro_dynamicsintegration_orderstatus


## Attributes

 - Sales - external_id (external_id)

