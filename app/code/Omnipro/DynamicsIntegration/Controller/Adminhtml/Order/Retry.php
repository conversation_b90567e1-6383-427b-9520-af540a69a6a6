<?php

namespace Omnipro\DynamicsIntegration\Controller\Adminhtml\Order;

use Magento\Backend\App\Action;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Framework\Exception\LocalizedException;
use Omnipro\DynamicsIntegration\Api\OrderInterface;

class Retry extends Action
{
    protected $orderRepository;
    private OrderInterface $order;

    public function __construct(
        Action\Context $context,
        OrderRepositoryInterface $orderRepository,
        OrderInterface $order

    ) {
        parent::__construct($context);
        $this->orderRepository = $orderRepository;
        $this->order = $order;
    }

    public function execute()
    {
        $orderId = $this->getRequest()->getParam('order_id');
        try {
            $order = $this->orderRepository->get($orderId);
            $response = $this->order->sendOrderToErp($order, true);
            if ($response == true) {
                $this->messageManager->addSuccessMessage(__('La acción se volvió a intentar con éxito.'));
            } else {
                $response = (empty($response)) ? 'Error al procesar la orden' : $response;
                $this->messageManager->addErrorMessage($response);
            }
        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage(__('Error: %1', $e->getMessage()));
        }

        $this->_redirect('sales/order/index');
    }
}
