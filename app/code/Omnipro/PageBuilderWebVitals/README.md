# Omnipro_PageBuilderWebVitals Module

### Author
OmniPro Team

### Package
Omnipro_PageBuilderWebVitals

## Description

Magento's Page Builder is a great core module (as of 2.4.3), which makes it easy to create content-rich pages, but there are a couple of elements that are missing:

- Image dimensions
- Link labels
- loading="lazy" tags

Why are these important?...

Google's Web Vitals audits contain improvement suggestions that help increase both the Performance and Accessibility metrics. The 2 relating to Magento's Page Builder are:

- Image elements do not have explicit width and height
- Links do not have a discernible name

**Image elements do not have explicit width and height**

Once this module is installed, you will have 2 new fields allowing you to add a width and height to each image you upload via Page Builder. Adding these dimensions will ensure that the image you have uploaded will pass the Google Web Vital audit

**Links do not have a discernible name**

This is recommended by Google when a tags don't contain text. When adding an image with a link, the a tag only contains an image, but one way to ensure this a tag passes the Web Vitals audit is to add an aria-label tag to the link itself.
Once this module is installed, you will have a new field allowing you to add a label to the link tag which will ensure the link passes the audit.

Adding image dimensions and link labels will ensure those elements pass the audit, but it can also have a knock on effect that also helps improve your CLS score (Content Layout Shift)

## PageBuilderWebVitals module by Omni.pro
- [Main Functionalities](#main-functionalities)
- [Installation](#installation)
- [Specifications](#specifications)

## Main Functionalities
- Implement 'tags' or snippets of code to track user behavior and interactions. 

## Installation
\* = in production please use the `--keep-generated` option

### Type 1: Zip file
- Unzip the zip file in `app/code/Omnipro`
- Enable the module by running `php bin/magento module:enable Omnipro_PageBuilderWebVitals`
- Flush the cache by running `php bin/magento cache:flush`

### Requirements
- Magento 2.4.6

### Copyright
Copyright © 2024 OmniPro (https://omni.pro/)
