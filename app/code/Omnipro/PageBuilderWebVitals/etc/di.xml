<?xml version="1.0"?>
<!--
/**
 * Copyright © 2024 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> Team
 * @date        25/01/24, 05:00 PM
 * @category    Omnipro
 * @module      Omnipro/PageBuilderWebVitals
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/e†c/config.xsd">
    <virtualType name="DefaultWYSIWYGValidator">
        <arguments>
            <argument name="attributesAllowedByTags" xsi:type="array">
                <item name="img" xsi:type="array">
                    <item name="loading" xsi:type="string">loading</item>
                </item>
                <item name="iframe" xsi:type="array">
                    <item name="loading" xsi:type="string">loading</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
</config>
