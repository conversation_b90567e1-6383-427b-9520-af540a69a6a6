<?xml version="1.0"?>
<!--
/**
 * Copyright © 2024 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> Team
 * @date        25/01/24, 05:00 PM
 * @category    Omnipro
 * @module      Omnipro/PageBuilderWebVitals
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_PageBuilder:etc/content_type.xsd">
    <type name="image">
        <appearances>
            <appearance name="full-width">
                <elements>
                    <element name="desktop_image">
                        <attribute name="desktop_width_attribute" source="width" />
                        <attribute name="desktop_height_attribute" source="height"/>
                        <attribute name="loading_attribute" source="loading"/>
                    </element>
                    <element name="mobile_image">
                        <attribute name="mobile_width_attribute" source="width" />
                        <attribute name="mobile_height_attribute" source="height"/>
                        <attribute name="loading_attribute" source="loading"/>
                    </element>
                    <element name="link">
                        <attribute name="aria_label_attribute" source="aria-label"/>
                    </element>
                </elements>
            </appearance>
        </appearances>
    </type>
</config>

