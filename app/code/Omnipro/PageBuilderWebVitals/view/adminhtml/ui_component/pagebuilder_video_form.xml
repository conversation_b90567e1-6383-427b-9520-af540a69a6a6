<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © 2024 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> Team
 * @date        31/01/24, 02:00 PM
 * @category    Omnipro
 * @module      Omnipro/PageBuilderWebVitals
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="seo" sortOrder="30">
        <settings>
            <label translate="true">Search Engine Optimization</label>
            <collapsible>true</collapsible>
            <opened>true</opened>
        </settings>
        <field name="title_attribute" sortOrder="10" formElement="input">
            <settings>
                <label translate="true">Title Attribute</label>
                <validation>
                    <rule name="validate-string" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
    </fieldset>
    <fieldset name="webvitals" sortOrder="40">
        <settings>
            <label translate="true">Core Web Vitals</label>
            <collapsible>true</collapsible>
            <opened>true</opened>
        </settings>
        <field name="loading_attribute" sortOrder="10" formElement="checkbox">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="default" xsi:type="string">lazy</item>
                </item>
            </argument>
            <settings>
                <dataType>boolean</dataType>
                <label translate="true">Add Lazy Loading Tag</label>
                <dataScope>loading_attribute</dataScope>
            </settings>
            <formElements>
                <checkbox>
                    <settings>
                        <valueMap>
                            <map name="false" xsi:type="string">eager</map>
                            <map name="true" xsi:type="string">lazy</map>
                        </valueMap>
                        <prefer>toggle</prefer>
                    </settings>
                </checkbox>
            </formElements>
        </field>        
    </fieldset>
</form>
