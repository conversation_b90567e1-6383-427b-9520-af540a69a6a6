<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © 2024 - Omnipro (https://www.omni.pro/)
 * <AUTHOR> Team
 * @date        25/01/24, 05:00 PM
 * @category    Omnipro
 * @module      Omnipro/PageBuilderWebVitals
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="general">
        <field name="aria_label_attribute" sortOrder="25" formElement="input">
            <settings>
                <label translate="true">Link Description</label>
                <notice translate="true">A detailed description of the link will ensure that Core Web Vitals audits are passed; for example: "Buy now" will fail; whereas "Discover our new range of products" will pass</notice>
                <validation>
                    <rule name="validate-string" xsi:type="boolean">true</rule>
                </validation>
            </settings>
        </field>
    </fieldset>
    <fieldset name="webvitals" sortOrder="40">
        <settings>
            <label translate="true">Core Web Vitals</label>
            <collapsible>true</collapsible>
            <opened>true</opened>
        </settings>
        <field name="desktop_width_attribute" sortOrder="10" formElement="input">
            <settings>
                <label translate="true">Desktop Image Width</label>
                <addAfter translate="true">px</addAfter>
                <additionalClasses>
                    <class name="admin__field-medium">true</class>
                </additionalClasses>
                <validation>
                    <rule name="validate-number" xsi:type="boolean">true</rule>
                    <rule name="greater-than-equals-to" xsi:type="number">0</rule>
                </validation>
            </settings>
        </field>
        <field name="desktop_height_attribute" sortOrder="20" formElement="input">
            <settings>
                <label translate="true">Desktop Image Height</label>
                <addAfter translate="true">px</addAfter>
                <additionalClasses>
                    <class name="admin__field-medium">true</class>
                </additionalClasses>
                <validation>
                    <rule name="validate-number" xsi:type="boolean">true</rule>
                    <rule name="greater-than-equals-to" xsi:type="number">0</rule>
                </validation>
            </settings>
        </field>
        <field name="mobile_width_attribute" sortOrder="30" formElement="input">
            <settings>
                <label translate="true">Mobile Image Width</label>
                <addAfter translate="true">px</addAfter>
                <additionalClasses>
                    <class name="admin__field-medium">true</class>
                </additionalClasses>
                <validation>
                    <rule name="validate-number" xsi:type="boolean">true</rule>
                    <rule name="greater-than-equals-to" xsi:type="number">0</rule>
                </validation>
            </settings>
        </field>
        <field name="mobile_height_attribute" sortOrder="40" formElement="input">
            <settings>
                <label translate="true">Mobile Image Height</label>
                <addAfter translate="true">px</addAfter>
                <additionalClasses>
                    <class name="admin__field-medium">true</class>
                </additionalClasses>
                <validation>
                    <rule name="validate-number" xsi:type="boolean">true</rule>
                    <rule name="greater-than-equals-to" xsi:type="number">0</rule>
                </validation>
            </settings>
        </field>
        <field name="loading_attribute" sortOrder="50" formElement="checkbox">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="default" xsi:type="string">lazy</item>
                </item>
            </argument>
            <settings>
                <dataType>boolean</dataType>
                <label translate="true">Add Lazy Loading Tag</label>
                <dataScope>loading_attribute</dataScope>
            </settings>
            <formElements>
                <checkbox>
                    <settings>
                        <valueMap>
                            <map name="false" xsi:type="string">eager</map>
                            <map name="true" xsi:type="string">lazy</map>
                        </valueMap>
                        <prefer>toggle</prefer>
                    </settings>
                </checkbox>
            </formElements>
        </field>
    </fieldset>
</form>
