"Link Description","Descripción del enlace"
"A detailed description of the link will ensure that Core Web Vitals audits are passed; for example: ""Buy now"" will fail; whereas ""Discover our new range of products"" will pass","Una descripción detallada del enlace garantizará que se pasen las auditorías de Core Web Vitals; por ejemplo: ""Comprar ahora"" fallará; mientras que ""Descubra nuestra nueva gama de productos"" pasará"
"Core Web Vitals","Core Web Vitals"
"Desktop Image Width","Ancho de la imagen de dispositivos de escritorio"
"Desktop Image Height","Altura de la imagen de dispositivos de escritorio"
"Mobile Image Width","Ancho de la imagen de dispositivos móviles"
"Mobile Image Height","Altura de la imagen de dispositivos móviles"
"Height can be a single number with any valid CSS unit (50px, 50%, 50em, 50vh) or a calculation (50% + 50px).","La altura puede ser un único número con cualquier unidad CSS válida (50px, 50%, 50em, 50vh) o un cálculo (50% + 50px)."
"Width can be a single number with any valid CSS unit (50px, 50%, 50em, 50vh) or a calculation (50% + 50px).","El ancho puede ser un único número con cualquier unidad CSS válida (50px, 50%, 50em, 50vh) o un cálculo (50% + 50px)."
"Add Lazy Loading Tag","Añadir la etiqueta de carga diferida"
"Search Engine Optimization","Optimización de motores de búsqueda"
"Title Attribute","Título Atributo"
"px","px"
