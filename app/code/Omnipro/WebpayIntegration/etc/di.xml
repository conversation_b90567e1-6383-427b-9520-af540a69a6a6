<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Transbank\Webpay\Controller\Transaction\CommitWebpay">
        <plugin name="omnipro_webpayintegration_capture_webpay_transaction_id"
                type="Omnipro\WebpayIntegration\Plugin\CaptureWebpayTransactionId"
                sortOrder="10"
                disabled="false"/>
    </type>
    <type name="Omnipro\WebpayIntegration\Plugin\CaptureWebpayTransactionId">
        <arguments>
            <argument name="checkoutSession" xsi:type="object">Magento\Checkout\Model\Session\Proxy</argument>
            <argument name="orderRepository" xsi:type="object">Magento\Sales\Api\OrderRepositoryInterface\Proxy</argument>
            <argument name="webpayOrderDataFactory" xsi:type="object">Transbank\Webpay\Model\WebpayOrderDataFactory</argument>
            <argument name="searchCriteriaBuilder" xsi:type="object"> Magento\Framework\Api\SearchCriteriaBuilder</argument> 
            <argument name="request" xsi:type="object">Magento\Framework\App\RequestInterface</argument> 
            <argument name="json" xsi:type="object"> Magento\Framework\Serialize\Serializer\Json</argument>
        </arguments>
    </type>
    <preference for="Transbank\Webpay\Observer\EmailObserver" type="Omnipro\WebpayIntegration\Observer\CustomEmailObserver" />
    <type name="Transbank\Webpay\Model\Config\ConfigProvider">
        <plugin name="omnipro_webpayintegration_config_provider_plugin" type="Omnipro\WebpayIntegration\Plugin\ConfigProviderPlugin" />
    </type>
</config>
