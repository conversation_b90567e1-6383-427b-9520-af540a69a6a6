<?xml version="1.0"?>
<!--
/**
 * Created on Fri Sep 06 2024
 * <AUTHOR> <<EMAIL>>
 * @package Omnipro\WebpayIntegration\etc
 * @copyright Copyright (c) 2024 Omnipro (https://www.omni.pro/)
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="checkout_onepage_controller_success_action">
        <observer name="transbank_invoice" disabled="true" />
    </event>
    <event name="sales_order_creditmemo_save_before">
        <observer name="transbank_refund" disabled="true" />
        <observer name="omnipro_webpay_integration_refund_observer"
            instance="Omnipro\WebpayIntegration\Observer\RefundObserver" />
    </event>
</config>
