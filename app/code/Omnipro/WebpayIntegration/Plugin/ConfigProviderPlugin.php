<?php

namespace Omnipro\WebpayIntegration\Plugin;

use Transbank\Webpay\Model\Config\ConfigProvider as Subject;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * ConfigProviderPlugin
 */
class ConfigProviderPlugin
{
    /**
     * StoreManager
     *
     * @var StoreManagerInterface
     */
    private StoreManagerInterface $storeManager;

    /**
     * ScopeConfig
     *
     * @var ScopeConfigInterface
     */
    private ScopeConfigInterface $scopeConfig;

    /**
     * ConfigProviderPlugin construct
     *
     * @param StoreManagerInterface $storeManager
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        StoreManagerInterface $storeManager,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->storeManager = $storeManager;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * AroundGetPluginConfig
     *
     * @param Subject $subject
     * @param callable $proceed
     * @return void
     */
    public function aroundGetPluginConfig(Subject $subject, callable $proceed)
    {
        $scope = ScopeInterface::SCOPE_STORE;
        $storeCode = $this->storeManager->getStore()->getCode();

        $config = [
            'ENVIRONMENT' => $this->scopeConfig->getValue(Subject::SECURITY_CONFIGS_ROUTE . 'environment', $scope, $storeCode),
            'COMMERCE_CODE' => $this->scopeConfig->getValue(Subject::SECURITY_CONFIGS_ROUTE . 'commerce_code', $scope, $storeCode),
            'API_KEY' => $this->scopeConfig->getValue(Subject::SECURITY_CONFIGS_ROUTE . 'api_key', $scope, $storeCode),
            'URL_RETURN' => 'checkout/transaction/commitwebpay',
            'ECOMMERCE' => 'magento',
            'new_order_status' => $subject->getOrderPendingStatus(),
            'payment_successful_status' => $subject->getOrderSuccessStatus(),
            'payment_error_status' => $subject->getOrderErrorStatus(),
            'new_email_order' => $subject->getEmailSettings(),
            'invoice_settings' => $subject->getInvoiceSettings(),
        ];

        return $config;
    }

    /**
     * AroundGetOrderPendingStatus Return config by store
     *
     * @param Subject $subject
     * @param callable $proceed
     * @return void
     */
    public function aroundGetOrderPendingStatus(Subject $subject, callable $proceed)
    {
        $storeCode = $this->storeManager->getStore()->getCode();

        return $this->scopeConfig->getValue(
            Subject::ORDER_CONFIGS_ROUTE . 'new_order_status',
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * AroundGetOrderSuccessStatus Return config by store
     *
     * @param Subject $subject
     * @param callable $proceed
     * @return void
     */
    public function aroundGetOrderSuccessStatus(Subject $subject, callable $proceed)
    {
        $storeCode = $this->storeManager->getStore()->getCode();

        return $this->scopeConfig->getValue(
            Subject::ORDER_CONFIGS_ROUTE . 'payment_successful_status',
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * AroundGetOrderErrorStatus Return config by store
     *
     * @param Subject $subject
     * @param callable $proceed
     * @return void
     */
    public function aroundGetOrderErrorStatus(Subject $subject, callable $proceed)
    {
        $storeCode = $this->storeManager->getStore()->getCode();

        return $this->scopeConfig->getValue(
            Subject::ORDER_CONFIGS_ROUTE . 'payment_error_status',
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * AroundGetPluginConfigOneclick
     *
     * @param Subject $subject
     * @param callable $proceed
     * @return void
     */
    public function aroundGetPluginConfigOneclick(Subject $subject, callable $proceed)
    {
        $scope = ScopeInterface::SCOPE_STORE;
        $storeCode = $this->storeManager->getStore()->getCode();

        $config = [
            'ENVIRONMENT' => $this->scopeConfig->getValue(Subject::SECURITY_CONFIGS_ROUTE_ONECLICK . 'environment', $scope, $storeCode),
            'COMMERCE_CODE' => $this->scopeConfig->getValue(Subject::SECURITY_CONFIGS_ROUTE_ONECLICK . 'commerce_code', $scope, $storeCode),
            'CHILD_COMMERCE_CODE' => $this->scopeConfig->getValue(Subject::SECURITY_CONFIGS_ROUTE_ONECLICK . 'child_commerce_code', $scope, $storeCode),
            'TRANSACTION_MAX_AMOUNT' => $this->scopeConfig->getValue(Subject::SECURITY_CONFIGS_ROUTE_ONECLICK . 'transaction_max_amount', $scope, $storeCode),
            'API_KEY' => $this->scopeConfig->getValue(Subject::SECURITY_CONFIGS_ROUTE_ONECLICK . 'api_key', $scope, $storeCode),
            'URL_RETURN' => 'checkout/transaction/commitoneclick',
            'ECOMMERCE' => 'magento',
            'title' => $subject->getOneclickTitle(),
            'new_order_status' => $subject->getOneclickOrderPendingStatus(),
            'payment_successful_status' => $subject->getOneclickOrderSuccessStatus(),
            'payment_error_status' => $subject->getOneclickOrderErrorStatus(),
            'new_email_order' => $subject->getOneclickEmailSettings(),
            'invoice_settings' => $subject->getOneclickInvoiceSettings(),
        ];

        return $config;
    }

    /**
     * AroundGetOneclickOrderPendingStatus Return config by store
     *
     * @param Subject $subject
     * @param callable $proceed
     * @return void
     */
    public function aroundGetOneclickOrderPendingStatus(Subject $subject, callable $proceed)
    {
        $storeCode = $this->storeManager->getStore()->getCode();

        return $this->scopeConfig->getValue(
            Subject::ORDER_CONFIGS_ROUTE_ONECLICK . 'new_order_status',
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * AroundGetOneclickOrderSuccessStatus Return config by store
     *
     * @param Subject $subject
     * @param callable $proceed
     * @return void
     */
    public function aroundGetOneclickOrderSuccessStatus(Subject $subject, callable $proceed)
    {
        $storeCode = $this->storeManager->getStore()->getCode();

        return $this->scopeConfig->getValue(
            Subject::ORDER_CONFIGS_ROUTE_ONECLICK . 'payment_successful_status',
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }

    /**
     * AroundGetOneclickOrderErrorStatus Return config by store
     *
     * @param Subject $subject
     * @param callable $proceed
     * @return void
     */
    public function aroundGetOneclickOrderErrorStatus(Subject $subject, callable $proceed)
    {
        $storeCode = $this->storeManager->getStore()->getCode();

        return $this->scopeConfig->getValue(
            Subject::ORDER_CONFIGS_ROUTE_ONECLICK . 'payment_error_status',
            ScopeInterface::SCOPE_STORE,
            $storeCode
        );
    }
}
