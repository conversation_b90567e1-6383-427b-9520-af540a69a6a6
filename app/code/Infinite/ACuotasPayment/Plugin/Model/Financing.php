<?php
/**
 * Created on Fri Jan 10 2025
 * <AUTHOR> Team
 * @package Infinite\ACuotasPayment\Model
 * @category Infinite
 * @copyright Copyright (c) 2025 Infinite
 */
declare(strict_types=1);

namespace Infinite\ACuotasPayment\Plugin\Model;

use Magento\Quote\Api\Data\CartInterface;
use Apurata\Financing\Helper\ConfigReader;
use Magento\Framework\App\RequestInterface;
use Magento\Store\Model\StoreManagerInterface;
use Apurata\Financing\Model\Financing as ApurataFinancing;
use Psr\Log\LoggerInterface;

class Financing
{
    /**
     * Financing constructor
     *
     * @param LoggerInterface $logger
     * @param RequestInterface $request
     * @param ConfigReader $configReader
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        private LoggerInterface $logger,
        private RequestInterface $request,
        private ConfigReader $configReader,
        private StoreManagerInterface $storeManager
    ) {}

    public function aroundIsAvailable(
        ApurataFinancing $subject,
        callable $proceed,
        CartInterface $quote = null
    ) {
        if (!$this->configReader->allowHttp() && !$this->request->isSecure()) {
            $this->logger->error(__('Apurata only support https'));
            return false;
        }

        $currency = $this->storeManager->getStore()->getCurrentCurrency()->getCode();
        if ($currency != 'PEN') {
            $this->logger->error(__('Apurata only support currency=PEN. Current currency = %1', $currency));
            return false;
        }

        $landingConfig = $subject->getLandingConfig();
        if ($quote) {
            $orderTotalAmount = $quote->getGrandTotal();
            $minAmount = $landingConfig->min_amount;
            $maxAmount = $landingConfig->max_amount;
            if (!$landingConfig || $orderTotalAmount < $minAmount || $orderTotalAmount > $maxAmount) {
                $this->logger->error(
                    __(
                        'Min allowed amount is %1 and Max allowed amount is %2. Grand Total: %3',
                        $minAmount,
                        $maxAmount,
                        $orderTotalAmount
                    )
                );

                return false;
            }
        }

        return true;
    }
}
