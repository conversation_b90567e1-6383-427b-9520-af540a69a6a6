//  *
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Variables
//  _____________________________________________

@product-name-link__color: @text__color;
@product-name-link__color__active: @text__color;
@product-name-link__color__hover: @text__color;
@product-name-link__color__visited: @text__color;

@product-name-link__text-decoration: none;
@product-name-link__text-decoration__active: @link__hover__text-decoration;
@product-name-link__text-decoration__hover: @link__hover__text-decoration;
@product-name-link__text-decoration__visited: @link__hover__text-decoration;

@product-item__hover__background-color: @color-white;
@product-item__hover__box-shadow: 3px 4px 4px 0 rgba(0, 0, 0, .3);

@product-price__muted__color: @color-gray40;

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    //  Product Lists
    .products {
        margin: @indent__l 0;

        .towishlist {
            position: absolute;
            top: 0;
            right: 5px;
            z-index: 1;
        }

        .product-item-details {
            padding-top: 8px;
            padding-bottom: var(--product-item-details__padding-bottom, 10px);
        }

        .saving {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 500;
        }
    }

    .product-item .action.towishlist {
        display: none;

        &::before {
            content: url('@{baseDir}images/icons/icon__wishlist.svg');
            width: 24px;
        }
    }

    .product-image-container {
        width: 100% !important; //Override img style
    }

    .page-products {
        .products.grid + .toolbar-products {
            display: none;
        }

        .product-items {
            display: grid;
            grid-template-columns: repeat(var(--products-grid__columns, 2), 1fr);
            column-gap: var(--products-grid__column-gap, 16px);
    
            > :not(.product-item) {
                grid-column: ~'1 / -1';
            }
        }
    }

    .product-item-link {
        text-decoration: @product-name-link__text-decoration;
    }

    .product-item {
        position: relative;
    }

    .page-products .product {
        &-items {
            .lib-inline-block-space-container();
            &:extend(.abs-reset-list all);
        }

        &-item {
            .lib-inline-block-space-item();
            vertical-align: top;
            height: max-content;

            .products-grid & {
                display: inline-block;
                width: 100%;
            }

            &:extend(.abs-add-box-sizing all);

            &-name {
                font-weight: 700;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
            }

            &-info {
                max-width: 100%;

                .page-products & {
                    width: 100%;
                }
            }

            &-actions {
                font-size: 0;

                > * {
                    font-size: 1.4rem;
                }

                .actions-secondary {
                    display: inline-block;
                    font-size: 1.4rem;
                    vertical-align: middle;

                    > button.action {
                        .lib-button-reset();
                    }

                    > .action {
                        &:before {
                            margin: 0;
                        }
                        line-height: 35px;
                        text-align: center;
                        width: 35px;

                        span {
                            &:extend(.abs-visually-hidden all);
                        }
                    }
                }

                .actions-primary {
                    display: inline-block;
                    vertical-align: middle;
                    
                    > .stock.unavailable {
                        line-height: 1;
                        padding-bottom: @indent__s;
                        padding-right: 24px;
                        padding-top: @indent__s;
                    }
                }
            }

            &-description {
                margin: @indent__m 0;
            }

            .product-reviews-summary {
                .rating-summary {
                    margin: 0 4px 0 0;
                }

                .reviews-actions {
                    font-size: @font-size__s;
                    margin-top: 5px;
                }
            }

            .price-box {
                display: flex;
                flex-direction: column;

                .price {
                    font-size: 14px;
                    font-weight: 300;
                    white-space: nowrap;
                }

                .price-label {
                    .lib-css(color, @product-price__muted__color);
                    font-size: @font-size__s;
                }
            }

            .old-price {
                margin: @indent__xs 0;

                .price {
                    font-weight: normal;
                }
            }

            .regular-price {
                .price-label {
                    display: none;
                }
            }

            .minimal-price {
                .price-container {
                    display: block;
                }
            }

            .minimal-price-link {
                margin-top: @indent__xs;
            }

            .price-from,
            .price-to {
                margin: 0;
            }

            .tocompare {
                .lib-icon-font-symbol(
                @icon-compare-full
                );
            }

            .tocart {
                .lib-font-size(13px);
                border-radius: 0;
                line-height: 1;
                padding-bottom: @indent__s;
                padding-top: @indent__s;
                white-space: nowrap;
            }
        }
    }

    .column.main {
        .product {
            &-items {
                margin-left: 0;
            }

            &-item {
                padding-left: 0;
            }
        }
    }

    .price-container {
        .price {
            .lib-font-size(14);
        }

        .price-including-tax + .price-excluding-tax,
        .weee {
            margin-top: @indent__xs;
        }

        .price-including-tax + .price-excluding-tax,
        .weee,
        .price-including-tax + .price-excluding-tax .price,
        .weee .price,
        .weee + .price-excluding-tax:before,
        .weee + .price-excluding-tax .price {
            .lib-font-size(11);
        }

        .weee {
            &:before {
                content: '(' attr(data-label) ': ';
            }

            &:after {
                content: ')';
            }

            + .price-excluding-tax {
                &:before {
                    content: attr(data-label) ': ';
                }
            }
        }
    }

    .products-list {
        .product {
            &-item {
                display: table;
                width: 100%;

                &-info {
                    display: table-row;
                }

                &-photo {
                    display: table-cell;
                    padding: 0 @indent__l @indent__l 0;
                    vertical-align: top;
                    width: 1%;
                }

                &-details {
                    display: table-cell;
                    vertical-align: top;
                }
            }
        }

        .product-image-wrapper {
            &:extend(.abs-reset-image-wrapper all);
        }
    }

    .price-name-container {
        display: flex;
        flex-direction: column;
    }

    .category-name {
        display: block;
        font-size: 1.2rem;
        margin: 12px 0;
        grid-row-start: 2;
    }

    //Override Omnipro Swatch
    .catalogsearch-result-index,
    .catalog-category-view,
    .page-with-filter {
        .product-item .product-item-info .product-item-details {
            position: unset;
        }
    }

    .product-items {
        .slick-slide {
            > div {
                margin-right: 16px;
            }

            .product-item {
                max-width: 400px;
            }
        }
    }

    .related,
    .upsell {
        clear: both;
        
        .block-title {
            margin-bottom: var(--related-upsell-title__margin-bottom, 24px);
            
            strong {
                font-size: 2.4rem;
                font-weight: 500;;
            }
        }
    }
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .products-list .product {
        &-item {
            table-layout: fixed;

            &-photo {
                padding: 0 @indent__s @indent__s 0;
                width: 30%;
            }
        }
    }
    
    .product-item-actions {
        .actions-primary {
            display: block;
        }
    }

    .price-name-container {
        flex-direction: column;

        .price-box {
            order: 1;
        }
    }

    .page-products .swatch-option {
        --swatch-option-img-color-size: 22px;
    }

    .related,
    .upsell {
        .price-name-container .price-box {
            margin-bottom: 10px;
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {

    .product-item-actions {
        display: block;

        .products-grid & {
            margin: -@indent__s 0 @indent__s;
        }

        .actions-primary + .actions-secondary {
            display: inline-block;
            vertical-align: middle;

            > .action {
                line-height: 35px;
                text-align: center;
                width: 35px;

                &:last-child {
                    margin-right: 0;
                }
            }
        }

        .actions-primary {
            display: inline-block;
        }
    }

    .product-item .swatch-option.color {
        visibility: hidden;

        &:not([data-option-label='Blanco']) {
            border: none;
        }
    }

    .product-item:hover {
        .towishlist {
            display: block;
        }

        .swatch-option.color {
            visibility: visible;
        }
    }

    .page-products.page-layout-1column,
    .page-products.page-layout-3columns {
        .products-grid {
            .product-item {
                margin-left: 0;
                width: calc(~'(100% - 4%) / 3');

                &:nth-child(3n + 1) {
                    margin-left: 0;
                }
            }
        }
    }

    .page-layout-1column .block.widget .products-grid .product-item,
    .page-layout-3columns .block.widget .products-grid .product-item {
        .product-item-inner {
            box-shadow: 3px 6px 4px 0 rgba(0, 0, 0, .3);
            margin: 9px 0 0 -1px;
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .page-products {
        --products-grid__columns: 3;
        --products-grid__column-gap: 16px;
        --product-item-details__padding-bottom: 50px;
        --product-item-saving__bottom: 35px;

        .swatch-option {
            --swatch-option-img-color-size: 22px;
        }

        &.page-layout-2columns-left {
            .column.main {
                width: 75%;
            }

            .product-items {
                padding: 0 6px;
            }
        }
    }

    .page-products.page-layout-1column {
        .products-grid {
            .product-item {
                width: 100%/4;
            }
        }
    }

    .page-products.page-layout-3columns {
        .products-grid {
            .product-item {
                width: 100%/2;
            }
        }
    }

    .related,
    .upsell {
        --related-upsell-title__margin-bottom: 32px;
        margin-left: 80px;
    }
}

