<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
use Magento\Framework\App\Action\Action;

// phpcs:disable Magento2.Templates.ThisInTemplate
// phpcs:disable Generic.Files.LineLength.TooLong

/** @var \Magento\CatalogWidget\Block\Product\ProductsList $block */
?>
<?php
/**
 * Product carousel widget template
 *
 * @var \Magento\Framework\Escaper $escaper
 */
?>
<?php if ($exist = ($block->getProductCollection() && $block->getProductCollection()->getSize())): ?>
    <?php
    $type = 'widget-product-carousel';

    $image = 'new_products_content_widget_grid';
    $items = $block->getProductCollection()->getItems();

    $showWishlist = true;
    $showCompare = true;
    $showCart = true;
    $templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
    ?>
    <ol class="product-items <?= /* @noEscape */ $type ?>">
        <?php $iterator = 1; ?>
        <?php foreach ($items as $_item): ?>
            <?= /* @noEscape */ ($iterator++ == 1) ? '<li class="product-item">' : '</li><li class="product-item">' ?>
            <div class="product-item-info">
                <a href="<?= $block->escapeUrl($block->getProductUrl($_item)) ?>" class="product-item-photo">
                    <?= $block->getImage($_item, $image)->toHtml() ?>
                </a>

                <?php if ($_item->getData('ribbon_text')): ?>
                    <div class="custom-ribbon">
                        <span><?= $_item->getData('ribbon_text') ?></span>
                    </div>
                <?php endif; ?>

                <div class="product-item-details">
                    <strong class="product-item-name">
                        <a title="<?= $block->escapeHtml($_item->getName()) ?>"
                           href="<?= $block->escapeUrl($block->getProductUrl($_item)) ?>"
                           class="product-item-link">
                            <?= $block->escapeHtml($_item->getName()) ?>
                        </a>
                    </strong>

                    <?php if ($templateType): ?>
                        <?= $block->getReviewsSummaryHtml($_item, $templateType) ?>
                    <?php endif; ?>

                    <?= $block->getProductPriceHtml($_item, $type) ?>

                    <div class="additional-information">
                        <?php 
                            $additionalInfo = $_item->getData('additional_information');
                        ?>
                        <?php if (isset($additionalInfo) && !empty($additionalInfo)): ?>
                            <?= $additionalInfo ?>
                        <?php endif; ?>
                    </div>

                    <div class="product-item__secondary-badge">
                        <?php 
                            $secondaryBadge = $_item->getData('secondary_badge');
                        ?>
                        <?php if (isset($secondaryBadge) && !empty($secondaryBadge)): ?>
                            <?= $secondaryBadge ?>
                        <?php endif; ?>
                    </div>

                    <?= $block->getProductDetailsHtml($_item) ?>

                    <?php if ($showWishlist || $showCompare || $showCart): ?>
                        <div class="product-item-inner">
                            <div class="product-item-actions">
                                <?php if ($showCart): ?>
                                    <div class="actions-primary">
                                        <?php if ($_item->isSaleable()): ?>
                                            <?php $postParams = $block->getAddToCartPostParams($_item); ?>
                                            <form data-role="tocart-form" data-product-sku="<?= $block->escapeHtml($_item->getSku()) ?>" action="<?= $block->escapeUrl($postParams['action']) ?>" method="post">
                                                <input type="hidden" name="product" value="<?= $block->escapeHtmlAttr($postParams['data']['product']) ?>">
                                                <input type="hidden" name="<?= /* @noEscape */ Action::PARAM_NAME_URL_ENCODED ?>" value="<?= /* @noEscape */ $postParams['data'][Action::PARAM_NAME_URL_ENCODED] ?>">
                                                <?= $block->getBlockHtml('formkey') ?>
                                                <button type="submit"
                                                        title="<?= $block->escapeHtml(__('Add to Cart')) ?>"
                                                        class="action tocart primary">
                                                    <span><?= $block->escapeHtml(__('Add to Cart')) ?></span>
                                                </button>
                                            </form>
                                            <script type="text/x-magento-init">
                                            {
                                                "[data-role=tocart-form], .form.map.checkout": {
                                                    "catalogAddToCart": {
                                                        "product_sku": "<?= $escaper->escapeJs($_item->getSku()); ?>"
                                                    }
                                                }
                                            }
                                            </script>
                                        <?php else: ?>
                                            <?php if ($_item->getIsSalable()): ?>
                                                <div class="stock available"><span><?= $block->escapeHtml(__('In stock')) ?></span></div>
                                            <?php else: ?>
                                                <div class="stock unavailable"><span><?= $block->escapeHtml(__('Out of stock')) ?></span></div>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                                <?php if ($showWishlist || $showCompare): ?>
                                    <div class="actions-secondary" data-role="add-to-links">
                                        <?php if ($this->helper(\Magento\Wishlist\Helper\Data::class)->isAllow() && $showWishlist): ?>
                                            <a href="#"
                                               data-post='<?= /* @noEscape */ $block->getAddToWishlistParams($_item) ?>' class="action towishlist" data-action="add-to-wishlist" title="<?= $block->escapeHtmlAttr(__('Add to Wish List')) ?>">
                                                <span><?= $block->escapeHtml(__('Add to Wish List')) ?></span>
                                            </a>
                                        <?php endif; ?>
                                        <?php if ($block->getAddToCompareUrl() && $showCompare): ?>
                                            <?php $compareHelper = $this->helper(\Magento\Catalog\Helper\Product\Compare::class);?>
                                            <a href="#" class="action tocompare" data-post='<?= /* @noEscape */ $compareHelper->getPostDataParams($_item) ?>' title="<?= $block->escapeHtmlAttr(__('Add to Compare')) ?>">
                                                <span><?= $block->escapeHtml(__('Add to Compare')) ?></span>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?= ($iterator == count($items) + 1) ? '</li>' : '' ?>
        <?php endforeach ?>
    </ol>
<?php endif;?>
