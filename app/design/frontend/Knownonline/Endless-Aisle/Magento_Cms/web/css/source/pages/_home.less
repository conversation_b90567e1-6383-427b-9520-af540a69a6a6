//
//  Common (Both desktop and mobile)
//  _____________________________________________
& when (@media-common = true) {
    .home-fila-row {
        .home-banner-hero {
            .slick-arrow {
                &.slick-prev:active, 
                &.slick-next:active, 
                &.slick-prev:focus, &.slick-next:focus, 
                &.slick-prev:not(.primary), 
                &.slick-next:not(.primary) {
                    background: none;
                }
                &.slick-prev {

                    &::before {
                        .background-icon('icon__arrow-left', 46px, 46px);

                        display: block;
                    }
                }

                &.slick-next {

                    &::before {
                        .background-icon('icon__arrow-right', 46px, 46px);

                        display: block;
                    }
                }
            }
        }
        .home-grid-featured {
            .pagebuilder-column {
                font-size: 0;
                position: relative;
            }
            figure {
                overflow: hidden;
            }
            img {
                transition: transform .5s; 
                &:hover {
                    transform: scale(1.1);
                }
            }
            .goto-action {
                position: absolute;
                border-radius: 50%;
                width: 45px;
                height: 45px;
               background-color: transparent;
                border: solid 2px @theme__color__primary-alt;
                color: @theme__color__primary-alt;
                .pagebuilder-button-primary {
                    width: 45px;
                    height: 45px;
                    margin: 0;
                    background-color: transparent;
                    border: none;
                    &:hover {
                        background-color: @theme__color__primary;
                    }
                    span {
                        display: block;
                        width: 45px;
                        height: 45px;
                        position: absolute;
                        bottom: 0px;
                        right: 0px;
                        z-index: 1;
                    }
                }
                &:hover {
                    background-color: @theme__color__primary;
                    border-color: @theme__color__primary;                    
                }
                &:after {
                    content: '\e61c';
                    font-family: 'luma-icons';
                    font-size: 22px;
                    position: absolute;
                    bottom: 7px;
                    right: 11px;
                    font-weight: 600;
                    cursor: pointer;
                    z-index: 0;
                }
            }
            .text-one,
            .text-two {
                color: @theme__color__primary-alt;
            }
            .text-one {
                position: absolute;
                text-transform: uppercase;
                font-weight: 600;
            }
            .text-two {
                position: absolute;
                max-width: 280px;
            }
        }
        .home-categories-grid-row,
        .home-categories-slider-row {
            .text-one p,
            .text-two p{
                margin: 0;
            }
            .text-one {
                color: @fila__color__gray-text;
            }
            .text-two {
                color: @theme__color__primary;
                font-size: 26px;
                font-weight: 500;
            }
            .text-three {
                font-size: 16px;
            }
            .pagebuilder-button-secondary {
                .button-secondary();
                width: 97px;
                font-size:16px;
                background-color: @theme__color__primary-alt;
                color: @theme__color__primary;
                border-color: @fila__color__gray-border;
                text-transform: inherit;
                padding: 8px 14px;
                padding-bottom: 6px;
                &:hover {
                    background-color: @theme__color__primary;
                    color: @theme__color__primary-alt;
                }
            }
            .slick-arrow {
                &.slick-prev:active, 
                &.slick-next:active, 
                &.slick-prev:focus, &.slick-next:focus, 
                &.slick-prev:not(.primary), 
                &.slick-next:not(.primary) {
                    background: none;
                }
                &.slick-prev {
                    top: 30%;
                    &::before {
                        .background-icon('icon__arrow-left', 36px, 36px);
                        filter: invert(100%) brightness(100%);
                        display: block;
                    }
                }

                &.slick-next {
                    top: 30%;
                    &::before {
                        .background-icon('icon__arrow-right', 36px, 36px);
                        filter: invert(100%) brightness(100%);
                        display: block;
                    }
                }
            }
        }
        .home-categories-grid-row {
            h2 {
                font-weight: 500;
            }
        }
        .home-categories-slider-row { 
            .slick-slide {
                .pagebuilder-column {
                    position: relative;
                    .pagebuilder-button-secondary {
                        position: absolute;
                        bottom: 6.3%;
                        left: 9%;
                        margin: 0;
                        text-transform: uppercase;
                        width: max-content;
                        background-color: @theme__color__primary;
                        color: @theme__color__primary-alt;
                        border-color: @theme__color__primary;
                        &:hover {
                            background-color: @theme__color__primary-alt;
                            color: @theme__color__primary;
                            border-color: @theme__color__primary;
                        }
                    }
                    span {
                        line-height: 14px;
                        vertical-align: bottom
                    }
                    div[data-content-type="buttons"] {
                        height: 0;
                    }
                }
            }
            .slick-arrow {
                &.slick-prev {
                    top: 46%;
                    left: 20px;
                }

                &.slick-next {
                    top: 46%;
                    right: 10%;
                }
            }
        }
        .home-featured-products-row {
            .slick-arrow {
                &.slick-prev {
                    top: 35%;
                    &::before {
                        .background-icon('icon__arrow-left', 36px, 36px);
                        filter: invert(100%) brightness(100%);
                        display: block;
                    }
                }

                &.slick-next {
                    top: 35%;
                    &::before {
                        .background-icon('icon__arrow-right', 36px, 36px);
                        filter: invert(100%) brightness(100%);
                        display: block;
                    }
                }
            }
            .product-item-info .product-item-inner,
            .product-item-info:hover .product-item-inner {
                display: none;
                > [class^='swatch-opt-'] {
                    display: none;
                }
                .product-item-actions {
                    .actions-secondary {
                        display: none;
                    }   
                }
            }
            .slick-prev:not(.primary), .slick-next:not(.primary) {
                background-color: transparent;
            }
            .product-item-name .product-item-link {
                font-size: 20px;
            }
            .product-item-details {
                > [class^='swatch-opt-'] {
                    display: none;
                }
            }
        }
    }
    &.cms-index-index {
        .column.main {
            background-color: @fila__color__gray;
            padding-bottom: 0;
        }
    }
    .endless-aisle-row {
        background-color: @theme__color__primary-alt;
        border-radius: 15px;
        h2 {
            font-weight: 500;
            margin-top: 0;
        }
        .endless-aisle {
            form {
                position: relative;
                .action.search {
                    position: absolute;
                    top: 4px;
                    right: 25px;
                    background-color: transparent;
                    font-size: 0;
                    height: 35px;
                    border: none;
                    padding: 0;
                }
                .input-placeholder();
                .action.search:before {
                    .background-icon('icono-buscar', 30px, 30px);
                    margin: 0;
                    display: block;
                }
                input {
                    background-color: @fila__color__gray;
                    padding-left: 30px;
                }
            }
        }
    }
}

//  _____________________________________________
//  Mobile extra S
//  _____________________________________________
@media only screen and (min-width: @screen__xxs) and (max-width: 360px) {
    .home-featured-products-row {
        .products-grid .product-items,
        .product-item-details {
            width: ~"calc(100% - 50px)";
            margin: 0 auto;
        }
    }
}


//  _____________________________________________
//  Mobile
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .home-fila-row {
        .home-banner-hero {
            .slick-arrow {
                &.slick-prev {
                    left: -2px;
                }

                &.slick-next {
                    right: 30px;
                }
            }
        }
        .home-grid-featured {
            img.pagebuilder-mobile-only {
                width: 100%;
            }
            .text-one {
                font-size: 22px;
                bottom: 49px;
                left: 26px;
            }
            .text-two {
                font-size: 16px;
                bottom: 11px;
                left: 26px;
                line-height: 20px;
            }
            .goto-action {
                bottom: 30px;
                right: 24px;
            }
        }
        .home-second-banner {
            padding: 54px 0;
            img.pagebuilder-mobile-only {
                width: 100%;
            }
        }
        .home-categories-grid-row,
        .home-categories-slider-row {
            padding-top: 34px;
            padding-left: 27px;
            .text-one {
                padding-top: 17px;
            }
            .text-three {
                height: 42px;
                width: 225px;
                padding-bottom: 10px;
            }
            .slick-track {
                display: flex;
                gap: 14px;
            }
            .pagebuilder-column-line {
                overflow: hidden;
            }
            .slick-arrow {
                &.slick-prev {
                    left: -2px;
                }

                &.slick-next {
                    right: 30px;
                }
            }
            h2 {
                font-size: 20px;
            }
        }
        .home-categories-slider-row { 
            padding-left: 0;
            .slick-slide {
                .pagebuilder-column {
                    .pagebuilder-button-secondary {
                        bottom: 9%;
                        left: 9%;
                        padding: 6px 19px;
                        font-size: 14px;
                    }
                }
            }
        }
        .home-featured-products-row {
            padding-top: 34px;
            .products-grid .product-item, 
            .pagebuilder-column .product-items.widget-product-grid .product-item  {
                padding-left: 10% !important;
                padding-right: 10% !important;
            }
            .product.name a, 
            .product-item-name a, 
            .product-item-name a.product-item-link {
                line-height: 22px;
                padding-bottom: 10px;
            }
            .slick-arrow.slick-prev {
                left: 10px;
            }
            .slick-arrow.slick-next {
                right: 35px;
            }
            .product-item .price-box .price {
                font-size: 28px;
            }
            .product-item .price-box .old-price .price {
                font-size: 16px;
            }
        }
    }
    .endless-aisle-main-row {
        padding: 25px;
        .endless-aisle-row {
            height: 190px;
            padding: 0 18px;
            h2 {
                font-size: 20px;
            }
        }
    }
}

//  _____________________________________________
//  Desktop +
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .home-banner-hero {
        .slick-arrow {
            &.slick-prev {
                left: 100px;
            }

            &.slick-next {
                right: 100px;
            }
        }
    }
    .home-second-banner {
        padding: 72px 0;
    }
    .home-categories-grid-row,
    .home-categories-slider-row {
        padding-top: 74px;
        .pagebuilder-column-line {
            gap: 15px;
        }
        .text-one {
            padding-top: 29px;
        }
    }
    .home-categories-slider-row {
        .pagebuilder-column-line {
            width: 100vw !important;
            gap: 15px;
            .slick-track {
                display: flex;
                gap: 15px;
            }
        }
        .slick-slide {
            .pagebuilder-column {
                .pagebuilder-button-secondary {
                    bottom: 9%;
                    left: 9%;
                    padding: 16px 30px;
                    font-size: 20px;
                }
            }
        }
    }
    .home-featured-products-row {
        padding-top: 74px;
    }
    .home-fila-row {
        [data-content-type="block"] ~ [data-content-type="block"] {
            padding: 0 5%;
        }
        .home-featured-products-row {
            .slick-arrow.slick-prev {
                left: -20px;
            }
            .slick-arrow.slick-next {
                right: 0;
            }
        }
            
        [data-content-type="block"]:nth-of-type(3) {
            overflow-x: hidden;
        }
        .home-categories-slider-row {
            .slick-arrow {
                &.slick-prev::before {
                    width: 46px;
                    height: 46px;
                }

                &.slick-next::before {
                    width: 46px;
                    height: 46px;
                }
            }
        }
        .home-grid-featured {
            .text-one {
                font-size: 26px;
                bottom: 79px;
                left: 75px;
            }
            .text-two {
                font-size: 22px;
                bottom: 19px;
                left: 75px;
                line-height: 26px;
            }
            .goto-action {
                bottom: 65px;
                right: 73px;
            }
        }
    }
    .endless-aisle-row {
        max-width: 46.5%;
        height: 215px;
        margin: 0 auto;
        .endless-aisle {
            max-width: 73%;
            margin: 0 auto;
        }
    }
    // &#html-body {
    //     .endless-aisle-row {
    //         justify-content: center;
    //     }
    // }
} 

//  _____________________________________________
//  Desktop 1440+
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__xl) {

    .home-featured-products-row {
        ol.product-items.widget-product-grid {
            display: flex;
            justify-content: space-between;
            width: 100%;
        }
        .block.widget .products-grid .widget-product-grid.product-items .product-item {
            width: auto !important;
        }
    }
}