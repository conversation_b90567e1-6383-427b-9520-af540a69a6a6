/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

define(["jquery"], function ($) {
    "use strict";

    return function (widget) {
        $.widget("mage.SwatchRenderer", widget, {
            /**
             * Add size chart render
             *
             * @private
             */
            _addSizeChart: function (item, label, listLabel, options, select, input) {
                var container = this.element,
                    classes = this.options.classes,
                    customSizeChartUrl = this.options.customSizeChartUrl || '',
                    customSizeChartLinkLabel = this.options.customSizeChartLinkLabel || 'Size guide',
                    sizeChartLink = '',
                    sizeChartDialog = '';

                if (customSizeChartUrl) {
                    sizeChartLink =
                        '<div class="' + classes.attributeClass + ' size-guide">' +
                            '<a class="size-guide__link" href="' + customSizeChartUrl + '">' +
                                customSizeChartLinkLabel + '</a>' +
                        '</div>';
                    sizeChartDialog =
                        `<dialog class="sizechart-dialog" id="sizechartdialog">
                            <button class="close-size-chart" type="button">
                                <svg fill="currentColor" class="search-close-icon" width="18px" height="18px" viewBox="0 0 37 37">
                                    <path fill-rule="nonzero" d="M36.533 3.533L33 0 18.267 14.733 3.533 0 0 3.533l14.733 14.734L0 33l3.533 3.533L18.267 21.8 33 36.533 36.533 33 21.8 18.267z"></path>
                                </svg>
                            </button>
                            <div class="size-guide__size-chart"><img src="${customSizeChartUrl}" alt="Guide image" loading="lazy"></div>
                        </dialog>`;

                    $(document).on('click', '.size-guide__link, button.close-size-chart', function (e) {
                        e.preventDefault();
                        let dialog = document.getElementById('sizechartdialog');

                        if (e.currentTarget.classList.contains('size-guide__link')) {
                            dialog.showModal();

                            // Add a click event listener to close the dialog when clicking outside its content
                            let clickOutsideListener = function (event) {
                                if (event.target === dialog) {
                                    dialog.close();
                                    dialog.removeEventListener('click', clickOutsideListener); // Remove the listener
                                }
                            };

                            dialog.addEventListener('click', clickOutsideListener);
                        } else if (e.currentTarget.classList.contains('close-size-chart')) {
                            dialog.close();
                        }
                    });
                }

                // display size selection in an accordion
                container.append(
                    '<div class="' + classes.attributeClass + ' ' + item.code + '" ' +
                        'data-attribute-code="' + item.code + '" ' +
                        'data-attribute-id="' + item.id + '" ' +
                        'attribute-code="' + item.code + '" ' +
                        'attribute-id="' + item.id + '">' +
                    label +
                    sizeChartLink +
                    sizeChartDialog + '<div aria-activedescendant="" ' +
                                'tabindex="0" ' +
                                'aria-invalid="false" ' +
                                'aria-required="true" ' +
                                'role="listbox" ' + listLabel +
                                'class="' + classes.attributeOptionsWrapper + ' clearfix">' +
                                options +
                                select +
                            '</div>' +
                            input +
                    '</div>'
                );
            },
            /**
             * Render controls
             *
             * @private
             */
            _RenderControls: function () {
                var $widget = this,
                    container = this.element,
                    classes = this.options.classes,
                    chooseText = this.options.jsonConfig.chooseText,
                    showTooltip = this.options.showTooltip,
                    customSizeSelectorLabel = this.options.customSizeSelectorLabel || 'Pick a size',
                    customSizeSelectorAttrCodes = this.options.customSizeSelectorAttrCodes || [];

                $widget.optionsMap = {};

                $.each(this.options.jsonConfig.attributes, function () {
                    var item = this,
                        controlLabelId = 'option-label-' + item.code + '-' + item.id,
                        options = $widget._RenderSwatchOptions(item, controlLabelId),
                        select = $widget._RenderSwatchSelect(item, chooseText),
                        input = $widget._RenderFormInput(item),
                        listLabel = '',
                        label = '';

                    // are we customizing the size selector
                    let useCustomSizeSelector = !!customSizeSelectorLabel && customSizeSelectorAttrCodes.length && customSizeSelectorAttrCodes.indexOf(item.code) > -1;


                    // Show only swatch controls
                    if ($widget.options.onlySwatches && !$widget.options.jsonSwatchConfig.hasOwnProperty(item.id)) {
                        return;
                    }

                    if ($widget.options.enableControlLabel) {
                        let txt = !!useCustomSizeSelector ? customSizeSelectorLabel : item.label;
                        label +=
                            '<span id="' + controlLabelId + '" class="' + classes.attributeLabelClass + '">' +
                            $('<i></i>').text(txt).html() +
                            '</span>' +
                            '<span class="' + classes.attributeSelectedOptionLabelClass + '"></span>';
                    }

                    if ($widget.inProductList) {
                        $widget.productForm.append(input);
                        input = '';
                        listLabel = 'aria-label="' + $('<i></i>').text(item.label).html() + '"';
                    } else {
                        listLabel = 'aria-labelledby="' + controlLabelId + '"';
                    }

                    // Create new control
                    if (useCustomSizeSelector) {
                        $widget._addSizeChart(item, label, listLabel, options, select, input);
                    } else {
                        container.append(
                            '<div class="' + classes.attributeClass + ' ' + item.code + '" ' +
                                'data-attribute-code="' + item.code + '" ' +
                                'data-attribute-id="' + item.id + '">' +
                                label +
                                '<div aria-activedescendant="" ' +
                                    'tabindex="0" ' +
                                    'aria-invalid="false" ' +
                                    'aria-required="true" ' +
                                    'role="listbox" ' + listLabel +
                                    'class="' + classes.attributeOptionsWrapper + ' clearfix">' +
                                    options + select +
                                '</div>' + input +
                            '</div>'
                        );
                    }

                    $widget.optionsMap[item.id] = {};

                    // Aggregate options array to hash (key => value)
                    $.each(item.options, function () {
                        if (this.products.length > 0) {
                            $widget.optionsMap[item.id][this.id] = {
                                price: parseInt(
                                    $widget.options.jsonConfig.optionPrices[this.products[0]].finalPrice.amount,
                                    10
                                ),
                                products: this.products
                            };
                        }
                    });
                });

                if (showTooltip === 1) {
                    // Connect Tooltip
                    container
                        .find('[data-option-type="1"], [data-option-type="2"],' +
                            ' [data-option-type="0"], [data-option-type="3"]')
                        .SwatchRendererTooltip();
                }

                // Hide all elements below more button
                $('.' + classes.moreButton).nextAll().hide();

                // Handle events like click or change
                $widget._EventListener();

                // Rewind options
                $widget._Rewind(container);

                //Emulate click on all swatches from Request
                $widget._EmulateSelected($.parseQuery());
                $widget._EmulateSelected($widget._getSelectedAttributes());
            },

            /**
             * Render swatch options by part of config
             *
             * @param {Object} config
             * @param {String} controlId
             * @returns {String}
             * @private
             */
            _RenderSwatchOptions: function (config, controlId) {
                var optionConfig = this.options.jsonSwatchConfig[config.id],
                    optionClass = this.options.classes.optionClass,
                    sizeConfig = this.options.jsonSwatchImageSizeConfig,
                    moreLimit = parseInt(this.options.numberToShow, 10),
                    moreClass = this.options.classes.moreButton,
                    moreText = this.options.moreButtonText,
                    countAttributes = 0,
                    html = '';

                if (!this.options.jsonSwatchConfig.hasOwnProperty(config.id)) {
                    return '';
                }

                $.each(config.options, function (index) {
                    var id,
                        type,
                        value,
                        thumb,
                        label,
                        width,
                        height,
                        attr,
                        swatchImageWidth,
                        swatchImageHeight;

                    if (!optionConfig.hasOwnProperty(this.id)) {
                        return '';
                    }

                    // Add more button
                    if (moreLimit === countAttributes++) {
                        html += '<a href="#" class="' + moreClass + '"><span>' + moreText + '</span></a>';
                    }

                    id = this.id;
                    type = parseInt(optionConfig[id].type, 10);
                    value = optionConfig[id].hasOwnProperty('value') ?
                        $('<i></i>').text(optionConfig[id].value).html() : '';
                    thumb = optionConfig[id].hasOwnProperty('thumb') ? optionConfig[id].thumb : '';
                    width = _.has(sizeConfig, 'swatchThumb') ? sizeConfig.swatchThumb.width : 110;
                    height = _.has(sizeConfig, 'swatchThumb') ? sizeConfig.swatchThumb.height : 90;
                    label = this.label ? $('<i></i>').text(this.label).html() : '';
                    attr =
                        ' id="' + controlId + '-item-' + id + '"' +
                        ' index="' + index + '"' +
                        ' aria-checked="false"' +
                        ' aria-describedby="' + controlId + '"' +
                        ' tabindex="0"' +
                        ' data-option-type="' + type + '"' +
                        ' data-option-id="' + id + '"' +
                        ' data-option-label="' + label + '"' +
                        ' aria-label="' + label + '"' +
                        ' role="option"' +
                        ' data-thumb-width="' + width + '"' +
                        ' data-thumb-height="' + height + '"';

                    attr += thumb !== '' ? ' data-option-tooltip-thumb="' + thumb + '"' : '';
                    attr += value !== '' ? ' data-option-tooltip-value="' + value + '"' : '';

                    swatchImageWidth = _.has(sizeConfig, 'swatchImage') ? sizeConfig.swatchImage.width : 30;
                    swatchImageHeight = _.has(sizeConfig, 'swatchImage') ? sizeConfig.swatchImage.height : 20;

                    if (!this.hasOwnProperty('products') || this.products.length <= 0) {
                        attr += ' data-option-empty="true"';
                    }

                    if (type === 0) {
                        // Text
                        html += '<div class="' + optionClass + ' text" ' + attr + '>' + (value ? value : label) +
                            '</div>';
                    } else if (type === 1) {
                        // Color
                        html += '<div class="' + optionClass + ' color" ' + attr +
                            ' style="background: ' + value +
                            ' no-repeat center; background-size: initial;">' + '' +
                            '</div>';
                    } else if (type === 2) {
                        // Image
                        html += '<div class="' + optionClass + ' image" ' + attr +
                            ' style="background: url(' + value + ') no-repeat center; background-size: initial;width:' +
                            swatchImageWidth + 'px; height:' + swatchImageHeight + 'px">' + '' +
                            '</div>';
                    } else if (type === 3) {
                        // Clear
                        html += '<div class="' + optionClass + '" ' + attr + '></div>';
                    } else {
                        // Default
                        html += '<div class="' + optionClass + '" ' + attr + '>' + label + '</div>';
                    }
                });

                return html;
            },
        });
        return $.mage.SwatchRenderer;
    };
});
