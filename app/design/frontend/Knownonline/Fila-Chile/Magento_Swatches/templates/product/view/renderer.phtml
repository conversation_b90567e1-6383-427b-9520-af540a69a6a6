<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
?>
<?php
/** @var $block \Magento\Swatches\Block\Product\Renderer\Configurable */
/** @var \Magento\Swatches\ViewModel\Product\Renderer\Configurable $configurableViewModel */
$configurableViewModel = $block->getConfigurableViewModel();
// set default size chart URL
$attributes = $block->getProduct()->getAttributes();
$baseSizeGuideUrl = $this->helper('Magento\Cms\Helper\Wysiwyg\Images')->getBaseUrl() .'/wysiwyg/Guia_Talla/';
$imageUrl = null;
$attributeCodes = array_keys($attributes);
// set product specific size chart URL
if ( $block->getProduct()->getData('image_size') ) {
    $imageUrl = $baseSizeGuideUrl . $block->getProduct()->getData('image_size');
}
?>
<div class="swatch-opt" data-role="swatch-options"></div>

<script type="text/x-magento-init">
    {
        "[data-role=swatch-options]": {
            "Magento_Swatches/js/swatch-renderer": {
                "jsonConfig": <?= /* @noEscape */ $swatchOptions = $block->getJsonConfig() ?>,
                "jsonSwatchConfig": <?= /* @noEscape */ $swatchOptions = $block->getJsonSwatchConfig() ?>,
                "mediaCallback": "<?= $block->escapeJs($block->escapeUrl($block->getMediaCallback())) ?>",
                "gallerySwitchStrategy": "<?= $block->escapeJs($block->getVar('gallery_switch_strategy', 'Magento_ConfigurableProduct')) ?: 'replace'; ?>",
                "jsonSwatchImageSizeConfig": <?= /* @noEscape */ $block->getJsonSwatchSizeConfig() ?>,
                "showTooltip": <?= $block->escapeJs($configurableViewModel->getShowSwatchTooltip()) ?>,
                "customSizeSelectorLabel": "<?= __('Pick a size') ?>",
                "customSizeSelectorAttrCodes": ["size", "talla_accesorios", "talla_calzado", "talla_ropa"],
                "customSizeChartLinkLabel": "<?= __('Size Guide') ?>",
                "customSizeChartUrl": "<?= $imageUrl ?>"
            }
        },
        "*" : {
            "Magento_Swatches/js/catalog-add-to-cart": {}
        }
    }
</script>
