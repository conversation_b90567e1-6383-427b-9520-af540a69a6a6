<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <body>
        <referenceBlock name="sidebar.main.account_nav">
            <arguments>
                <argument name="block_title" translate="true" xsi:type="string">Newsletter Subscription</argument>
                <argument name="block_css" xsi:type="string">block-collapsible-nav</argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="breadcrumbs">
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">home</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string" translate="true">Beginning</item>
                    <item name="label" xsi:type="string" translate="true">Beginning</item>
                    <item name="link" xsi:type="string">{{baseUrl}}</item>
            </argument>
            </action> 
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">account</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string" translate="true">My account</item>
                    <item name="label" xsi:type="string" translate="true">My account</item>
                    <item name="link" xsi:type="string">{{baseUrl}}/customer/account</item>
                </argument>
            </action> 
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">account-newsletter</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string" translate="true">Newsletter Subscription</item>
                    <item name="label" xsi:type="string" translate="true">Newsletter Subscription</item>
                    <item name="link" xsi:type="string">{{baseUrl}}/newsletter/manage</item>
                </argument>
            </action>
        </referenceBlock>

        <referenceContainer name="content">
            <block class="Magento\Cms\Block\Block" name="newsletter_block" before="-">
                <arguments>
                    <argument name="class" xsi:type="string">gift-card</argument>
                    <argument name="block_id" xsi:type="string">umbro-info-account-newsletter</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
