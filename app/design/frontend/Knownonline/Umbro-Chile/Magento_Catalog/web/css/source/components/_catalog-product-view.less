//
//  Common (Both desktop and mobile)
//  _____________________________________________
& when (@media-common = true) {
    .catalog-product-view {
        .page-main {
            padding-top: 20px;
        }
        .product-info-main {
            position: relative;
            .breadcrumbs {
                margin-top: -84px;
                position: absolute;
                display: block;
                .item:not(:last-child):after {
                    content: ' / ';
                } 
            }
            select.swatch-select.size {
                display: none;
            }
            a.action.towishlist {
                float: right;
                color: @theme__color__primary;
                font-size: 15px;
                align-items: center;
                padding-top: 25px;
                &:before {
                    .background-icon('icon-heart', 15px, 15px);
                }
            }
            .page-title-wrapper h1 {
                font-weight: bold;
                margin-bottom: 5px;
                color: @umbro__color__black-alt;
            }
            .product-info-stock-sku {
                float: left;
                text-align: left;
                padding: 0;
                .product.attribute.sku .type {
                    margin: 0;
                    &:after {
                        content: ':';
                    }
                }

                .stock.available, 
                .stock.unavailable,
                .configurable-variation-qty {
                    display: none;
                }
                .product.attribute.sku {
                    color: @umbro__color__black-alt;
                }
            }
            .product-add-form {
                padding-top: 0;
            }
            .price-box {   
                display: inline-flex;
                flex-direction: column-reverse;
                span.price-label {
                    display: none;
                }
                .price {
                    color: @umbro__color__option-selected;
                }
                .old-price .price {
                    text-decoration: line-through;
                    color: @umbro__color__option-gray;
                }
                .price-container > span:last-child {
                    height: 30px;
                    display: block;
                }
            }
            .product-info-price  {
                display: inline-flex;
                align-items: center;
                gap: 6px;
                .old-price .price-wrapper .price {
                    font-size: 18px;
                }
                .normal-price  .price-wrapper .price {
                    font-size: 30px;
                    font-weight: bold;
                }
                .custom-label {
                    background-color: @theme__color__primary;
                    color: @theme__color__primary-alt;
                    font-size: 18px;
                    font-weight: bold;
                    width: 60px;
                    height: 30px;
                    margin-top: 10px;
                    text-align: center;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    span {
                        vertical-align: middle;
                    }
                }
            }
            .product-options-wrapper {
                .swatch-opt {
                    margin: 0;
                }
                .swatch-option {
                    margin: 6px 12px 6px 0;
                    &:focus {
                        box-shadow: none;
                    }
                    &.disabled::after {
                        width: 100%;
                    }
                }
            }    
            .swatch-attribute.size {
                flex-wrap: wrap;
            }        
            .swatch-attribute.size .swatch-option {
                background-color: @theme__color__primary-alt;
                color: @umbro__color__option-gray;
                border: solid 1px @umbro__color__option-gray-border;
                display: flex;
                justify-content: center;
                align-items: center;
                outline: unset;
                width: 40px;
                height: 40px;
                font-size: 18px;
                padding: 0;
                min-width: unset;
                &.selected{
                    background-color: @umbro__color__option-selected;
                    color: @theme__color__primary-alt;
                    border-color: @umbro__color__option-selected;
                }
            }
            .swatch-attribute.size-guide {
                order: 5;
                padding-left: 50px;
                padding-bottom: 10px;
                border: none;
                a.size-guide__link {
                    font-size: 15px;
                    font-weight: bold;
                    color: @theme__color__primary;
                }
            }
            button.close-size-chart {
                span {
                    display: none;
                }
            }
            .swatch-option.image,
            .swatch-option.color {
                min-width: 50px;
                min-height: 50px;
                padding: 0;
                background-size: contain !important;
                border: solid 1px @umbro__color__option-gray-border;
            }
            .swatch-option.image.selected,
            .swatch-option.image:not(.disabled):hover, 
            .swatch-option.color:not(.disabled):hover {
                outline: none;
                border-color: @umbro__color__option-selected;
            }

            .swatch-attribute {
                display: inline-flex;
                align-items: center;
                width: 100%;
                .swatch-attribute-label {
                    margin-right: 14px;
                    &:after {
                        content: ':';
                    }
                }
                .swatch-attribute-selected-option {
                    display: none;
                }
            }
            .qty-counter-group {
                display: flex;
                flex-flow: nowrap;
                align-items: center;

                &__button {
                    display: flex;
                    padding: var(--qty-counter-group__padding, 8px 14px);
                    border-color:  @umbro__color__gray-border;
                    background-color: @theme__color__primary-alt;
                    display: flex;
                    align-items: center;
                    &:hover,
                    &:focus {
                        background-color: @umbro__color__gray-border;
                    }
                }

                &__icon {
                    display: inline-block;

                    &--decrease {
                        .background-icon('icon__minus', 15px, 15px);
                    }

                    &--increase {
                        .background-icon('icon__plus', 15px, 15px);
                    }
                }
                input[type="number"] {
                    text-align: center;
                    border-left: none;
                    border-right: none;
                    font-weight: bold;
                }
            }
            .product-info-price,
            .swatch-attribute {
                border-bottom: 1px solid @umbro__color__gray-border;
                margin: 0;
            }
            .box-tocart {
                .action.primary.tocart {
                    background-color: @umbro__color__orange;
                    border: none;
                    text-transform: uppercase;
                    font-weight: bold;
                }
                label.label {
                    font-weight: bold;
                    font-size: 15px;
                }
            }
            mo-product-page {
                display: block;
                padding-bottom: 20px;
                border-bottom: 1px solid @umbro__color__gray-border;
                .mo-product-page {
                    flex-direction: row-reverse;
                    gap: 20px; 
                    background-color: @theme__color__primary-alt;
                    border: none; 
                    max-width: ~"calc(100% - 16px)";
                }
            }
            .product-stock-availability-box {
                margin: 20px 0;
                span.product-storepickup-title {
                    font-weight: bold;
                }
                .product-storepickup-option {
                    padding-bottom: 10px;
                }
                .product-storepickup-text {
                    display: flex;
                    flex-direction: column;
                }
                .product-storepickup-link {
                    text-decoration: underline;
                    cursor: pointer;
                }
                .product-storepickup {
                    &-option {
                        display: flex;
                        flex-flow: row nowrap;
                        gap: 20px;
                        align-items: center;

                        &::before {
                            display: inline-block;
                            width: 40px;
                            height: 40px;
                            content: '';
                        }

                        &.__storepickup::before {
                            background: no-repeat url('@{baseDir}images/icons/icon__store-pickup.svg') center;
                            background-size: 30px;
                        }

                        &.__delivery::before {
                            background: no-repeat url('@{baseDir}images/icons/icon__delivery.svg') center;
                            background-size: 30px;
                            filter: invert(100%) brightness(100%);
                        }
                    }
                }
            }
        }
        .product.info.detailed {
            .product.data.items > .item.title > .switch,
            .product.data.items > .item.content {
                border: none;
            }
            .product.data.items .item.title.active a:after {
                display: none;
            }
            .product.data.items > .data.item.content {
                border-top: solid 1px @umbro__color__option-gray-border;
                > * {
                    font-size: 16px;
                    color: @umbro__color__option-selected;
                }
            }
            .product.data.items > .item.title > .switch,
            .product.data.items > .item.title.active > .switch {
                color: @theme__color__primary;
                font-weight: bold;
                text-transform: uppercase;
                padding: 0;
                font-family: 'ItalianPlateNo2';
            }
        }
        .block.related,
        .block.upsell {
            display: none;
            .title {
                font-weight: bold;
                display: flex;
                align-items: center;
                gap: 10px;
                font-family: 'ItalianPlateNo2';
                strong {
                    flex-shrink: 0;
                }
                hr {
                    width: 100%;
                    border-top: 3px solid @theme__color__primary;
                    vertical-align: bottom;
                }
            }
            .title strong {
                text-transform: uppercase;
            }
            .product-item-actions {
                margin: 0 -10px;
                .actions-primary,
                .tocart {
                    width: 100%;
                    span {
                        text-transform: uppercase;
                        font-weight: bold;
                    }
                }
                .action.primary {
                    background-color: @umbro__color__gray__primary;
                    border-color: @umbro__color__gray__primary;
                    &:hover,
                    &:focus {
                        background-color: @theme__color__primary;
                        border-color: @theme__color__primary;
                    }
                }
            }
            .slick-arrow-base (45px,45px);
            .slick-arrow {
                &.slick-prev:active, 
                &.slick-next:active, 
                &.slick-prev:focus, &.slick-next:focus, 
                &.slick-prev:not(.primary), 
                &.slick-next:not(.primary) {
                    background: none;
                }
                &.slick-prev {
                    &::before {
                        .background-icon('arrow-slick', 20px, 15px);
                        transform: rotate(180deg);
                        display: block;
                    }
                }

                &.slick-next {
                    &::before {
                        .background-icon('arrow-slick', 20px, 15px);
                        display: block;
                    }
                }
                &.slick-next.slick-arrow,
                &.slick-prev.slick-arrow {
                    border: solid 1px @umbro__color__arrow-gray-border;
                }
            }
        }
        .product.media {
            .fotorama-item.fotorama {
                border: solid 1px @umbro__color__gray-border;
            }
        }
        .modal-storepickup {
            .modal__content .options-availability.header:after {
                font-size: 15px;
            }
        }
        .pdp-featured-products-row {
            .section-heading {
                .pagebuilder-column-line {
                    flex-wrap: nowrap;
                }
            }
            .section-products {
                .slick-arrow-base (45px,45px);
                .slick-arrow {
                    &.slick-prev:active, 
                    &.slick-next:active, 
                    &.slick-prev:focus, &.slick-next:focus, 
                    &.slick-prev:not(.primary), 
                    &.slick-next:not(.primary) {
                        background: none;
                    }
                    &.slick-prev {
                        &::before {
                            .background-icon('arrow-slick', 20px, 15px);
                            transform: rotate(180deg);
                            display: block;
                        }
                    }

                    &.slick-next {
                        &::before {
                            .background-icon('arrow-slick', 20px, 15px);
                            display: block;
                        }
                    }
                    &.slick-next.slick-arrow,
                    &.slick-prev.slick-arrow {
                        border: solid 1px @umbro__color__arrow-gray-border;
                    }
                }
            }
        }
        .pagebuilder-column-line {
            .section-title {
                h2 {
                    font-weight: bold;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    span {
                        flex-shrink: 0;
                        font-family: @font-family-name__secondary;
                    }
                }
                hr {
                        width: 100%;
                        border-top: 2px solid @theme__color__primary;
                        vertical-align: bottom;
                }
            }

        }
    }
}

//  _____________________________________________
//  Mobile
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    .catalog-product-view {
        .product-info-main {
            margin-top: 50px;
            .page-title-wrapper h1 {
                font-size: 25px;
            }
            a.action.towishlist {
                display: none;
            }
            div.product-info-stock-sku {
                padding-bottom: 12px;
            }
            .product-info-price > *:last-child {
                padding: 0;
            }
            .product-info-price  {
                width: 100% !important;
            }
            .swatch-attribute.color {
                border-bottom: none;
            }
            .product-options-bottom {
                    margin: 0 -15px;
                    box-shadow: 0 -5px 10px 0 rgba(0, 0, 0, 0.1), 0 5px 10px 0 rgba(0, 0, 0, 0.1);
                    .box-tocart {
                        padding: 0 15px;
                    }
                }
            .qty-counter-group {
                &__button {
                    width: var(--qty-counter-group__width, 40px);
                    height: var(--qty-counter-group__height, 40px);
                }
                input[type="number"] {
                    height: 40px;
                    max-width: 50px;
                    font-size: 20px;
                }
            }
            .box-tocart {
                .fieldset {
                    display: flex;
                    justify-content: space-between;
                    flex-wrap: wrap;
                    gap: 0 16px;
                }
                .actions {
                    flex: 1;
                }
                .action.primary.tocart {
                    font-size: 16px;
                }
                label.label {
                    width: 100%; 
                    display: block;
                    padding: 7px 0 3px;
                }
                .field.qty,
                .actions {
                    display: inline-block;
                }
                .actions {
                    float: right;
                    .action.primary.tocart {
                        height: 40px;
                        padding: 7px 17px;
                    }
                }
            }
        }
        .product.info.detailed {
            padding-top: 60px;
            .product.data.items {
                margin-left: 0px;
                margin-right: 0px;    
            }
            .product.data.items > .item.title > .switch,
            .product.data.items > .item.title.active > .switch {
                font-size: 25px;
                margin-bottom: 10px;
            }
        }
        .custom-gallery--container {
            display: none;
        }
        .block.related,
        .block.upsell {
            order: 3;
            .title {
                hr {
                    display: none;
                }
                strong {
                    font-size: 25px;
                }
            }
            .products-grid .product-items .product-item {
                max-width: 380px;
            }
            .product-item-actions {
                .tocart {
                    span {
                        font-size: 14px;
                    }
                }
            }
            button.slick-prev.slick-arrow,
            button.slick-next.slick-arrow {
                margin-top: 20px;
                position: unset;
                display: none !important;
            }
            .slick-prev.slick-arrow {
                order: 1;
            }
            .slick-next.slick-arrow {
                order: 2;
            }
        }
        .product.media {
            .fotorama__arr--prev .fotorama__arr__arr {
                .background-icon('arrow-slick', 35px, 35px);
                transform: rotate(180deg);
                display: block;
                top: ~"calc(50% - 20px)";
            }
            .fotorama__arr--next .fotorama__arr__arr {
                .background-icon('arrow-slick', 35px, 35px);
            }
            .fotorama__arr.fotorama__arr--prev,
            .fotorama__arr.fotorama__arr--next {
                display: block !important;
            }
            .fotorama__arr--prev .fotorama__arr__arr,
            .fotorama__arr--next .fotorama__arr__arr{
                border: solid 1px @umbro__color__arrow-gray-border;
                background-size: 25px;
            }
            .fotorama--custom-counter {
                display: none;
            }
        }
        .pdp-featured-products-row {
            width: 100%;
            order: 1;
            .section-heading {
                .pagebuilder-column-line {
                    flex-wrap: nowrap;
                }
                hr {
                    display: none;
                }
            }
            &.first {
                .section-heading {
                    padding: 0 10px;
                    .pagebuilder-column-line {
                        border-top: solid 1px @umbro__color__arrow-gray-border;
                        h2 {
                            padding: 0;
                            margin-top: 40px;
                        }
                    }
                }
            }
            .section-products {
                .slick-slider .slick-track, .slick-slider .slick-list {
                    display: flex;
                    gap: 10px;
                }
                .product-items {
                    justify-content: center;
                }

                .slick-prev.slick-arrow,
                .slick-next.slick-arrow {
                    margin-top: 20px;
                    position: unset;
                }
                .slick-prev.slick-arrow {
                    order: 1;
                }
                .slick-next.slick-arrow {
                    order: 2;
                }
                .products-grid.grid {
                    padding: 0 10px;
                    margin-left: -5px;
                }
            }
        }
    }
}

//  _____________________________________________
//  Tablet
//  _____________________________________________
@media only screen and (min-device-width : @screen__s) and (max-device-width : @screen__m) {
    .catalog-product-view {
        .product-info-main {

        }
    }
}

//  _____________________________________________
//  Desktop +
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .catalog-product-view {
        .page-main {
            padding-left: 95px;
            padding-right: 95px;
        }
        &.page-layout-1column {
            .product.media {
                width: ~"calc(57% - 60px)";
            }
        }
        .product-info-main {
            border-left: solid 1px @umbro__color__gray-border;
            padding-left: 60px;
            width: 32%;
            margin-top: 20px;
            .page-title-wrapper.product {
                padding-top: 55px;
            }
            a.action.towishlist {
                display: flex;
            }
            .page-title-wrapper h1 {
                font-size: 30px;
            }
            .qty-counter-group {
                &__button {
                    width: var(--qty-counter-group__width, 45px);
                    height: var(--qty-counter-group__height, 45px);
                }
                input[type="number"] {
                    height: 45px;
                    max-width: 70px;
                    font-size: 20px;
                }
            }
            .box-tocart {
                .action.primary.tocart {
                    font-size: 22px;
                    width: 100%;
                }
                .field.qty {
                    margin-top: 15px;
                }
            }
        }
        .product.media {
                        // Custom Gallery Refactor Updates
            .custom-gallery--container__wrapper {
                width: 100%;
                z-index: 1;
                position: relative;
                margin-top: 20px;
                .custom-gallery--container {
                    display: flex;
                    flex-direction: row;
                    flex-wrap: wrap;
                    gap: 20px;
                    width: 100%;
                    justify-content: space-between;
                    .custom-gallery--image {
                        width: 46%;
                        height: auto;
                        max-height: 566px;
                        padding: 0;
                        margin: 0 0 10px 0;
                        cursor: pointer;
                        border: solid 1px @umbro__color__gray-border;
                    }
                }
            }
        }
        .product.info.detailed {
            padding-top: 50px;
            width: ~"calc(57% - 60px)";
            .product.data.items > .item.title > .switch,
            .product.data.items > .item.title.active > .switch {
                font-size: 35px;
                margin-top: -7px;
            }
        }
        .block.related,
        .block.upsell {
            margin: 0 auto;
            .title strong {
                font-size: 50px;
            }
            .product-item-actions {
                .tocart {
                    span {
                        font-size: 14px;
                    }
                }
            }
            .slick-arrow {
                &.slick-prev {
                    left: -55px;
                }

                &.slick-next {
                    right: -55px;
                }
            }
        }
        .pdp-featured-products-row {
            .section-products {
                width: 100%;
                margin: 0 auto;
                .slick-arrow {
                    &.slick-prev {
                        left: -45px;
                    }

                    &.slick-next {
                        right: -65px;
                    }
                }
                .pagebuilder-column [data-content-type='products'] .block.widget .products-grid .widget-product-grid.product-items .product-item {
                    max-width: unset;
                }
                .pagebuilder-column {
                    width: ~"calc(100% + 40px)" !important;
                    margin-left: -30px;
                }
                .slick-list.draggable .slick-track {
                        display: flex;
                        gap: 30px;
                }
            }
        }
        .pagebuilder-column-line {
            .section-title {
                h2 {
                    font-size: 50px;
                    padding-left: 0;
                }
                hr {
                    display: inline-block;
                }
            }
        }
    }
}

//  _____________________________________________
//  Desktop 1440+
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__xl) {
    .catalog-product-view {

    }
}
