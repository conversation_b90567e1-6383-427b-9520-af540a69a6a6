//
//  Common (Both desktop and mobile)
//  _____________________________________________
& when (@media-common = true) {
    .table-wrapper,
    .table-wrapper.orders-recent {
        border: none;
    }
    table > thead > tr > th {
        padding-bottom: 0;
    }
    address {
        max-width: 165px;
    }
    address::first-line {
        font-weight: bold;
        margin-bottom: 5px;
        text-transform: capitalize;
    }
    .order-details-items .item-options dt,
    .order-details-items .item.options dt,
    .order-details-items .item-options dd,
    .order-details-items .item.options dd {
        display: inline-block;
    }

    .order-details-items .item-options dd,
    .order-details-items .item.options dd {
        width: 75%;
        margin-bottom: 5px;
    }
    table.table {
        border-collapse: separate;
        border-spacing: 0 10px;
    }


    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > tbody > tr td[data-th]::before,
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > tbody > tr th[data-th]::before,
    .order-details-items .item-options dt,
    .order-details-items .item.options dt {
        font-weight: bold;
        text-transform: uppercase;
        content: attr(data-th);
    }

    .table-wrapper .table.table-order-items:not(.totals):not(.cart):not(.table-comparison),
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison),
    .additional-addresses table > {
        thead tr th {
            font-weight: bold;
            font-size: 16px;
            text-align: center;
        }

        tbody tr {
            &:nth-child(even)  {
                background-color: @umbro__color__table_row_one;
            }
            &:nth-child(odd)  {
                background-color: @umbro__color__table_row_two;
            }

            > td.col {
                padding: 0;
                font-size: 14px;
                color: @umbro__color__black-alt;
                border: none;
            }

            > td.col.total,
            > td.col.price,
            > td.col.subtotal {
                .price {
                    font-weight: 600;
                }
            }

            > td.col.actions {
                border: none;

                .action {
                    text-decoration: none;

                    &::before {
                        display: inline-block;
                        vertical-align: middle;
                    }
                    span {
                        color: @umbro__color__orange;
                        text-decoration: underline;
                    }
                }
            }

 
        }
    }


    .table-wrapper.reviews .table.table-reviews {
        tbody tr {
            > td.col.item .product-name a,
            > td.col.description {
                font-weight: 400;
                text-decoration: none;
                letter-spacing: -.36px;
            }

            > td.col.summary {
                .rating-summary .rating-result {
                    margin-left: 0;
                }
            }
        }
    }
}

//  _____________________________________________
//  Mobile
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__s) {
    .table-wrapper  {
        padding-top: 20px;
    }
    .table-wrapper .table.table-order-items:not(.totals):not(.cart):not(.table-comparison),
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) {
        tbody {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        tbody tr {
            display: flex;
            flex-flow: wrap;
            gap: 20px 0;
            padding: @indent__base 20px;
            justify-content: space-between;
            > td.col {
                display: flex;
                flex-direction: column;
                gap: 4px;
            }

            > td.col.item,
            > td.col.summary,
            > td.col.description,
            > td.col.id,
            > td.col.date,
            > td.col.total,
            > td.col.status,
            > td.col.name,
            > td.col.sku,
            > td.col.qty,
            > td.col.price,
            > td.col.subtotal {
                flex: 0 0 48%;
            }

            > td.col.actions {
                position: relative;
                flex: 0 0 100%;
                flex-direction: row;
                align-items: end;
                gap: 20px;
                &::before {
                    position: relative;
                    width: fit-content;
                    height: fit-content;
                }
                .action:after {
                    display: none;
                }
            }
        }
    }
}

//  _____________________________________________
//  Desktop +
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__s) {
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > tbody > tr td[data-th]::before,
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) > tbody > tr th[data-th]::before {
        display: none;
    }

    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) thead tr th,
    .table-wrapper .table.table-order-items:not(.totals):not(.cart):not(.table-comparison) thead tr th {

        &.col.actions {
            padding-left: 0;
        }
    }

    .table-wrapper .table.table-order-items:not(.totals):not(.cart):not(.table-comparison) tbody tr,
    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) tbody tr,
    .account .table-wrapper .table.table-additional-addresses-items tbody tr {

        > td.col.item,
        > td.col.summary,
        > td.col.description,
        > td.col.id,
        > td.col.date,
        > td.col.total,
        > td.col.status,
        > td.col.name,
        > td.col.sku,
        > td.col.qty,
        > td.col.price,
        > td.col.subtotal,
        > td.col.shipping,
        > td.col.actions {
            text-align: center;
            vertical-align: middle;
        }

        > td.col.total span.price {
            font-weight: 400;
        }

        > td.col.actions {
            display: flex;
            flex-direction: column;

            .action::after {
                display: none;
            }
        }


        > td.col.subtotal {
            padding-right: 24px;
        }
    }

    .account .column.main .order-details-items .table-wrapper .data.table > thead > tr > th {
        border-bottom: none;
    }

    .order-items.table-wrapper .col.price,
    .order-items.table-wrapper .col.qty,
    .order-items.table-wrapper .col.msrp {
        text-align: left;
    }

    .order-items.table-wrapper .col.subtotal {
        padding-right: 24px;
    }

    .table-wrapper.reviews .table.table-reviews:not(.totals):not(.cart):not(.table-comparison) tbody tr > td.col.description {
        padding-right: 36px;
        word-break: break-all;
    }

    .table-wrapper.reviews .table.table-reviews:not(.totals):not(.cart):not(.table-comparison) tbody tr > td.col.actions {
        padding: 24px 40px 0 0;
    }

    .table-wrapper .table:not(.totals):not(.cart):not(.table-comparison) thead tr th.col.description {
        width: 192px;
    }
}
