/**
 * <AUTHOR> Team
 * @copyright Copyright(c) 2024 OmniPro (https://omni.pro/)
 * @module    OmniPro/Coliseum
 */

define([
    'jquery',
    'domReady!'
], function($) {
    'use strict';

    // Configuration for MutationObserver to track DOM changes
    const config = { childList: true, subtree: true },
        swatchExist = '.columns',
        swatchOpt = '[class^="swatch-opt-"]',
        swatchAttr = `${swatchOpt} .swatch-attribute`,
        swatchOption = '.swatch-option',
        swatchOptionAttr = '.swatch-attribute-options',
        swatchNameClass = 'swatch-name',
        swatchNameSelector = '.swatch-name',
        addToCardOption = '.add-to-cart-options:not(.no-option) .show-options',
        productItem = '.product-item',
        swatchButtonGroup = '.swatch-buttons-group';

    let observer;

    /**
     * Activates click and mouseleave event listeners for swatch and cart button interactions.
     *
     * @param {HTMLElement} container - The container within which events are set up.
     */
    function triggerActive(container) {
        const context = container || document;

        // Toggle the "active" class on swatch buttons when the add-to-cart button is clicked
        $(context).find(addToCardOption).off('click').on('click', function() {
            const siblingSwatchGroup = $(this).parent().siblings(swatchButtonGroup);

            siblingSwatchGroup.toggleClass('active');

            // Add 'active' class to all swatchNameSelector elements except those with 'color' class
            siblingSwatchGroup.find(swatchNameSelector).each(function () {
                const swatchElement = $(this);

                if (!swatchElement.hasClass('color')) {
                    swatchElement.addClass('active');
                } else {
                    swatchElement.removeClass('active');
                    swatchElement.siblings().hide();
                }
            });
        });

        // Remove the "active" class when the mouse leaves the product item
        $(context).find(productItem).off('mouseleave').on('mouseleave', function() {
            $(this).find(swatchButtonGroup).removeClass('active');
        });
    }

    /**
     * Initializes swatches by finding swatch options within the container. If swatches
     * are not yet available, it retries until they are found.
     *
     * @param {HTMLElement} container - The DOM element within which swatches are initialized.
     */
    function initializeSwatches(container) {
        const context = container || document;

        /**
         * Checks if swatch elements exist, initializes them if found, otherwise retries after 100ms.
         */
        function checkSwatches() {
            if ($(context).find(swatchOpt).find(swatchOption).length) {
                $(context).find(swatchAttr).each(function() {
                    let _this = $(this),
                        option = _this.find(swatchOptionAttr),
                        swatchName = option.attr('aria-label'),
                        swatchDiv = `<div class="${swatchNameClass} ${swatchName}">${swatchName}</div>`;

                    // Only initialize swatch if it hasn't been initialized before
                    if (!_this.find(swatchNameSelector).length && !_this.data('initialized')) {
                        option.before(swatchDiv);
                        
                        // Click event to toggle siblings and manage 'active' class
                        _this.find(swatchNameSelector).on('click', function () {
                            const swatchElement = $(this);
                            const siblings = swatchElement.siblings();

                            siblings.toggle();

                            // Add 'active' class if the sibling is visible, remove if hidden
                            if (siblings.is(':visible')) {
                                swatchElement.addClass('active');
                            } else {
                                swatchElement.removeClass('active');
                            }
                        });

                        _this.data('initialized', true);
                    }
                });
                triggerActive(context);
            } else {
                setTimeout(checkSwatches, 100);
            }
        }

        checkSwatches();
    }

    /**
     * Handles new content being dynamically loaded (e.g., via infinite scroll) by reinitializing swatches
     * for the newly added elements.
     *
     * @param {HTMLElement} target - The DOM element containing the new content.
     */
    function handleNewContent(target) {
        observer.disconnect();
        setTimeout(function() {
            initializeSwatches(target);
            observer.observe(targetNode, config); 
        }, 300); 
    }

    // MutationObserver setup to track DOM changes
    const targetNode = document.querySelector(swatchExist);
    const callback = function(mutationsList) {
        for (const mutation of mutationsList) {
            if (mutation.addedNodes.length) {
                handleNewContent(mutation.target);
            }
        }
    };

    observer = new MutationObserver(callback);
    if (targetNode) {
        observer.observe(targetNode, config);
    }

    // Handle dynamic content loading events from Magento
    $(document).on('contentUpdated', function(event) {
        handleNewContent(event.target);
    });

    // Initialize swatches and setup event listeners when the document is ready
    $(document).ready(function() {
        triggerActive();
        initializeSwatches(targetNode);
    });
});
