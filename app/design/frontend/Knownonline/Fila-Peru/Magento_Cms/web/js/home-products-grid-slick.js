define([
    'jquery',
    'slick'
], function($) {
    'use strict';

    return function (config, element) {
        if (!element) return;

        const slickElement = $(element);
        const slickConfig = {
            infinite: false,
            centerMode: true,
            slidesToShow: 1,
            arrows: true,
            dots: false,
            variableWidth: true,
            responsive: [
                {
                    breakpoint: 8000,
                    settings: "unslick"
                },
                {
                    breakpoint: 1440,
                    settings: {
                        slidesToShow: 1,
                        centerMode: false
                    }
                },
                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 1
                    }
                },
                {
                    breakpoint: 600,
                    settings: {
                        slidesToShow: 1
                    }
                },
                {
                    breakpoint: 360,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        arrows: true
                    }
                }
            ]
        }

        slickElement.not('.slick-initialized').slick(slickConfig);

        $(window).resize(function(){
            let $windowWidth = $(window).width();

            if ($windowWidth < 1440) {
                slickElement.not('.slick-initialized').slick(slickConfig);
            }
        });
    }
});
